[PS] C:\TEMP>.\Exchange-Mailbox-Security-Audit.ps1 -DomainFilter "albaraka.com"
[INFO] PowerShell Version: 5.1.14393.8244 - Compatible
=================================================================================
Exchange Server Mailbox Security Audit Script
=================================================================================
Script Version: 1.6.7 (September 11, 2025)
Created By: E.Z. Consultancy
PowerShell Version: 5.1.14393.8244
PowerShell Edition: Desktop
Execution Host: BARAKAEXCH02
Execution User: ad21bar
Execution Time: 2025-09-14 15:50:20
Domain Filter: albaraka.com
Max Sample Size: 500 mailboxes
Output Location: .\Exchange-Mailbox-Security-Results.json
=================================================================================
Audit ID: 50387a1f-b1f9-4ea9-b252-f91fb4e5a145
=================================================================================

Step 1/7: Collecting Exchange environment metadata...
  [DEBUG] Starting domain-filtered mailbox counting...
  [DEBUG] Using client-side counting for Exchange 2016 compatibility
  [DEBUG] Retrieved 762 total mailboxes for counting
  [DEBUG] Client-side counting completed: 149 mailboxes match filter
  Domain filter applied: 149 mailboxes match filter
  [DEBUG] Domain breakdown:
    [DEBUG] Domain albaraka.com : 149 mailboxes
[SUCCESS] Environment metadata collected successfully
  Metadata sections: 17
Step 2/7: Performing administrator discovery...
  Performing administrator discovery across all domains...
  [ERROR] Administrator discovery failed: You cannot call a method on a null-valued expression.
[SUCCESS] Administrator discovery completed
Performing pre-flight validation...
[SUCCESS] Exchange PowerShell module is available
[SUCCESS] Exchange connectivity verified
Step 3/7: Assessing mailbox impersonation rights...
[SUCCESS] Mailbox impersonation rights assessed
Step 4/7: Assessing mailbox full access permissions...
  [DEBUG] Get-FilteredMailboxes called with ResultSize: 500
  [DEBUG] Domain filter enabled: True
  [DEBUG] Filtered domains: albaraka.com
  Applying domain filter for: albaraka.com
  [DEBUG] Using client-side filtering for Exchange 2016 compatibility
  [DEBUG] Retrieving all mailboxes for client-side filtering...
  [DEBUG] Retrieved 762 total mailboxes for filtering
  [DEBUG] Applying domain filter using Test-EmailDomainMatch function...
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
    [DEBUG] Mailbox matched: <EMAIL>
  [DEBUG] Final filtered mailbox count: 149
  Found 149 mailboxes matching domain filter
  [DEBUG] Sample filtered mailboxes:
    [DEBUG] - <EMAIL>
    [DEBUG] - <EMAIL>
    [DEBUG] - <EMAIL>
    [DEBUG] ... and 146 more
  Processed 50 of 149 mailboxes...
  Processed 100 of 149 mailboxes...
  [INFO] Cross-domain analysis skipped (insufficient data or administrator discovery failed)
[SUCCESS] Full access permissions assessed
Step 5/7: Assessing mailbox audit logging configuration...
WARNING: The object baraka.com/Resigned Staff/AIB-Resigned Staff/Zaheer A. Naseer has been corrupted or isn't
compatible with Microsoft support requirements, and it's in an inconsistent state. The following validation errors
happened:
WARNING: Could not convert property WindowsEmailAddress to type SmtpAddress. Error while converting string
'ad12bar@@albaraka.bh' to result type Microsoft.Exchange.Data.SmtpAddress: The email address "ad12bar@@albaraka.bh"
isn't correct. Please use this format: user name, the @ sign, followed by the domain name. For example,
tonysmith@contoso.<NAME_EMAIL>.
WARNING: The object baraka.com/Resigned Staff/Vendors/Abdul Vahab Abdulrasak has been corrupted or isn't compatible
with Microsoft support requirements, and it's in an inconsistent state. The following validation errors happened:
WARNING: Could not convert property WindowsEmailAddress to type SmtpAddress. Error while converting string
'<EMAIL> ' to result type Microsoft.Exchange.Data.SmtpAddress: The email address
"<EMAIL> " isn't correct. Please use this format: user name, the @ sign, followed by the domain name.
 For example, tonysmith@contoso.<NAME_EMAIL>.
WARNING: By default, only the first 1000 items are returned. Use the ResultSize parameter to specify the number of
items returned. To return all items, specify "-ResultSize Unlimited". Be aware that, depending on the actual number of
items, returning all items can take a long time and consume a large amount of memory. Also, we don't recommend storing
the results in a variable. Instead, pipe the results to another task or script to perform batch changes.
[SUCCESS] Audit logging configuration assessed
Step 6/7: Assessing Send-As permissions...
  Processed 50 of 149 mailboxes for Send-As...
  Processed 100 of 149 mailboxes for Send-As...
[SUCCESS] Send-As permissions assessed
Step 7/7: Assessing Send-On-Behalf permissions...
  Processed 50 of 149 mailboxes for Send-On-Behalf...
  Processed 100 of 149 mailboxes for Send-On-Behalf...
[SUCCESS] Send-On-Behalf permissions assessed

Generating compliance summary...

=== AUDIT DATA COLLECTION SUMMARY ===
Total audit sections collected: 8
  - MBX_4_1_SendAsPermissions : SUCCESS
  - MBX_1_1_ImpersonationRights : SUCCESS
  - AuditMetadata : SUCCESS
  - MBX_2_1_FullAccessPermissions : SUCCESS
  - MBX_3_1_AuditLogging : SUCCESS
  - ComplianceSummary : SUCCESS
  - AdministratorDiscovery : SUCCESS
  - MBX_5_1_SendOnBehalfPermissions : SUCCESS
=======================================

Generating JSON output...
  Converting 8 audit sections to JSON...
  Preparing objects for JSON serialization...
  JSON conversion successful. Output size: 259172 characters

=================================================================================
Exchange Server Mailbox Security Audit Completed
=================================================================================
Audit ID: 50387a1f-b1f9-4ea9-b252-f91fb4e5a145
End Time: 2025-09-14 16:18:20
Duration: 1679.94 seconds
Controls Assessed: 5 (with cross-domain analysis)
Domain Filter Applied: albaraka.com
Output File: .\Exchange-Mailbox-Security-Results.json

[SUCCESS] JSON audit results saved successfully
[SUCCESS] Send the file '.\Exchange-Mailbox-Security-Results.json' for compliance analysis
=================================================================================

AUDIT SUMMARY:
- Total Controls: 5
- Successful Assessments: 5
- Failed Assessments: 0
- Success Rate: 100%
[PS] C:\TEMP>