﻿<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Exchange Mailbox Security Audit Report - TechCorp International</title>
<style>
body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
.container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; }
.header h1 { margin: 0 0 10px 0; font-size: 2.5em; }
.dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
.card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-left: 4px solid #667eea; }
.card h3 { margin: 0 0 15px 0; color: #667eea; }
.card .value { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }
table { width: 100%; border-collapse: collapse; margin: 20px 0; }
th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
th { background-color: #f8f9fa; font-weight: bold; }
tr:hover { background-color: #f5f5f5; }
.badge { padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold; color: white; }
.risk-critical { background-color: #dc3545; }
.risk-high { background-color: #fd7e14; }
.risk-medium { background-color: #ffc107; color: #212529; }
.risk-low { background-color: #28a745; }
.status-compliant { background-color: #28a745; }
.status-review { background-color: #fd7e14; }
.status-non-compliant { background-color: #dc3545; }
.footer { text-align: center; margin-top: 40px; padding: 20px; border-top: 1px solid #ddd; color: #666; }

/* Permission Analysis Sections */
.permission-section { margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; }
.permission-list { margin-top: 15px; }
.permission-item { background: white; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 3px solid #e9ecef; }
.permission-description { font-weight: bold; color: #333; margin-bottom: 8px; }
.dept-info { font-size: 0.9em; margin: 5px 0; padding: 5px 10px; border-radius: 4px; }
.dept-info.same-dept { background: #d4edda; color: #155724; }
.dept-info.cross-dept { background: #f8d7da; color: #721c24; }
.risk-flag { background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; font-weight: bold; margin-left: 10px; }
.permission-details { font-size: 0.85em; color: #666; margin-top: 8px; font-style: italic; }

/* Cross-Domain Analysis Styles */
.cross-domain-section { border-left: 4px solid #dc3545 !important; }
.cross-domain-item { border-left: 3px solid #dc3545 !important; }
.cross-domain-badge { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }

/* Administrator Discovery Styles */
.admin-section { border-left: 4px solid #6f42c1 !important; }
.admin-item { border-left: 3px solid #6f42c1 !important; }
.admin-badge { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); }

/* Enhanced Risk Indicators */
.risk-critical-bg { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
.risk-high-bg { background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%); }
.risk-medium-bg { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
.risk-low-bg { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }

/* Enhanced Permission Item Styling */
.permission-item:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); transition: all 0.3s ease; }
.permission-description { font-size: 1.1em; line-height: 1.4; }

/* Domain Relationship Styling */
.domain-relationship { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: 1px solid #dee2e6; }
.cross-domain-warning { background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border: 1px solid #ffeaa7; }
.cross-domain-danger { background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border: 1px solid #f5c6cb; }

/* Administrator Classification Colors */
.admin-exchange { color: #dc3545; font-weight: bold; }
.admin-mailbox { color: #fd7e14; font-weight: bold; }
.admin-readonly { color: #28a745; font-weight: bold; }
.admin-business { color: #6c757d; font-weight: bold; }
</style>
</head>
<body>
<div class="container">
<div class="header">
<h1>Exchange Mailbox Security Audit Report</h1>
<p>Comprehensive Security Assessment and Compliance Analysis</p>
<p><strong>Organization:</strong> TechCorp International | <strong>Audit Date:</strong> 2025-09-14 10:15:30 | <strong>Audit ID:</strong> b8f3e2d1-4c5a-6789-bcde-f012345678ab</p>
</div>
<div class="dashboard">
<div class="card">
<h3>Overall Compliance Score</h3>
<div class='value' style='color: #28a745'>83.2%</div>
<p>Average compliance across all controls</p>
</div>
<div class="card">
<h3>Controls Assessment</h3>
<div class='value'>5</div>
<p>Compliant: 2 | Review Required: 3 | Non-Compliant: 0</p>
</div>
<div class="card">
<h3>Risk Distribution</h3>
<div class='value' style='color: #dc3545'>HIGH</div>
<p>Critical:  | High: 2 | Medium: 2 | Low: 0</p>
</div>
</div>
<h2>Security Control Analysis</h2>
<table>
<thead>
<tr><th>Control ID</th><th>Control Name</th><th>Risk Level</th><th>Compliance Status</th><th>Score</th><th>Finding</th></tr>
</thead>
<tbody>
<tr>
<td><strong>Delegation Management</strong><br><small>MBX-2.1</small></td>
<td>Mailbox Full Access Permissions</td>
<td><span class='badge risk-high'>High</span></td>
<td>WARN <span class='badge status-review-required'>Review Required</span></td>
<td><strong>72%</strong></td>
<td>High number of Full Access permissions - review required</td>
</tr>
<tr>
<td><strong>Compliance &amp; Monitoring</strong><br><small>MBX-3.1</small></td>
<td>Mailbox Audit Logging Configuration</td>
<td><span class='badge risk-medium'>Medium</span></td>
<td>WARN <span class='badge status-review-required'>Review Required</span></td>
<td><strong>96%</strong></td>
<td>Admin audit logging enabled</td>
</tr>
<tr>
<td><strong>Email Delegation</strong><br><small>MBX-4.1</small></td>
<td>Send-As Permissions</td>
<td><span class='badge risk-high'>High</span></td>
<td>OK <span class='badge status-compliant'>Compliant</span></td>
<td><strong>85%</strong></td>
<td>Send-As permissions appear reasonable</td>
</tr>
<tr>
<td><strong>Email Delegation</strong><br><small>MBX-5.1</small></td>
<td>Send-On-Behalf Permissions</td>
<td><span class='badge risk-medium'>Medium</span></td>
<td>OK <span class='badge status-compliant'>Compliant</span></td>
<td><strong>88%</strong></td>
<td>Send-On-Behalf permissions appear reasonable</td>
</tr>
<tr>
<td><strong>Access Control</strong><br><small>MBX-1.1</small></td>
<td>Mailbox Impersonation Rights</td>
<td><span class='badge risk-critical'>Critical</span></td>
<td>WARN <span class='badge status-review-required'>Review Required</span></td>
<td><strong>75%</strong></td>
<td>Limited impersonation rights - review assignments</td>
</tr>
</tbody>
</table>
<h2>Detailed Permission Analysis</h2>
<p>The following sections provide human-readable details of specific permission relationships identified during the audit:</p>
<div class="permission-section">
<h3 style='color: #fd7e14; border-bottom: 2px solid #fd7e14; padding-bottom: 10px;'>
Full Access Permissions <span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em;'>25 permissions</span>
</h3>
<p style='color: #666; font-style: italic; margin-bottom: 20px;'>Users with full access can read, modify, and delete items in target mailboxes</p>
<div class="permission-list">
<div class="permission-item">
<div class='permission-description'>jane.smith has full access to John Doe (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>executive.assistant has full access to Chief Executive Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>robert.wilson has full access to Finance Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>sarah.johnson has full access to HR Shared Mailbox (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>mike.anderson has full access to IT Support (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>lisa.brown has full access to Marketing Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>david.martinez has full access to Sales Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>amanda.taylor has full access to Legal Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>thomas.white has full access to Procurement Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>patricia.clark has full access to Quality Assurance (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>nancy.rodriguez has full access to Customer Service (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>kevin.lewis has full access to Research & Development (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>stephanie.walker has full access to Training Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>brian.hall has full access to Security Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>michelle.young has full access to Operations Manager (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>daniel.king has full access to Accounting Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>carol.wright has full access to Project Management Office (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>facilities.admin has full access to Facilities Management (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>facilities.admin has full access to Conference Room A (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>facilities.admin has full access to Conference Room B (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>executive.assistant has full access to Executive Board Room (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>external.user has full access to Shared Project Alpha (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>procurement.manager has full access to Vendor Communications (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>svc-compliance has full access to Compliance Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>audit.manager has full access to Internal Audit (<EMAIL>)</div>
</div>
</div>
</div>
<div class="permission-section">
<h3 style='color: #fd7e14; border-bottom: 2px solid #fd7e14; padding-bottom: 10px;'>
Send-As Permissions <span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em;'>23 permissions</span>
</h3>
<p style='color: #666; font-style: italic; margin-bottom: 20px;'>Users with Send-As permissions can send emails appearing to come from the target mailbox</p>
<div class="permission-list">
<div class="permission-item">
<div class='permission-description'>executive.assistant can send as Chief Executive Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>robert.wilson can send as Finance Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>lisa.brown can send as Marketing Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>david.martinez can send as Sales Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>sarah.johnson can send as HR Shared Mailbox (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>amanda.taylor can send as Legal Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>mike.anderson can send as IT Support (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>nancy.rodriguez can send as Customer Service (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>thomas.white can send as Procurement Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>stephanie.walker can send as Training Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>patricia.clark can send as Quality Assurance (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>kevin.lewis can send as Research & Development (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>brian.hall can send as Security Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>michelle.young can send as Operations Manager (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>daniel.king can send as Accounting Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>carol.wright can send as Project Management Office (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>svc-compliance can send as Compliance Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>audit.manager can send as Internal Audit (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>procurement.manager can send as Vendor Communications (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>facilities.admin can send as Facilities Management (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>external.user can send as Shared Project Alpha (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>finance.manager can send as Chief Financial Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>it.director can send as Chief Technology Officer (<EMAIL>)</div>
</div>
</div>
</div>
<div class="permission-section">
<h3 style='color: #ffc107; border-bottom: 2px solid #ffc107; padding-bottom: 10px;'>
Send-On-Behalf Permissions <span class='badge' style='background-color: #ffc107; color: white; font-size: 0.8em;'>34 permissions</span>
</h3>
<p style='color: #666; font-style: italic; margin-bottom: 20px;'>Users with Send-On-Behalf permissions can send emails on behalf of the target mailbox owner</p>
<div class="permission-list">
<div class="permission-item">
<div class='permission-description'>finance.manager can send on behalf of Chief Financial Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>sarah.johnson can send on behalf of HR Director (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>jennifer.garcia can send on behalf of Sales Manager (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>executive.assistant can send on behalf of Chief Executive Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>it.director can send on behalf of Chief Technology Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>lisa.brown can send on behalf of Marketing Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>michelle.young can send on behalf of Operations Manager (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>amanda.taylor can send on behalf of Legal Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>thomas.white can send on behalf of Procurement Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>patricia.clark can send on behalf of Quality Assurance (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>kevin.lewis can send on behalf of Research & Development (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>stephanie.walker can send on behalf of Training Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>brian.hall can send on behalf of Security Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>daniel.king can send on behalf of Accounting Department (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>carol.wright can send on behalf of Project Management Office (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>facilities.admin can send on behalf of Facilities Management (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>nancy.rodriguez can send on behalf of Customer Service (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>mike.anderson can send on behalf of IT Support (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>svc-compliance can send on behalf of Compliance Officer (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>audit.manager can send on behalf of Internal Audit (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>procurement.manager can send on behalf of Vendor Communications (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>external.user can send on behalf of Shared Project Alpha (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>jane.smith can send on behalf of John Doe (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>robert.wilson can send on behalf of Finance Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>david.martinez can send on behalf of Sales Team (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>sarah.johnson can send on behalf of HR Shared Mailbox (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>executive.assistant can send on behalf of Executive Board Room (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>facilities.admin can send on behalf of Conference Room A (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>facilities.admin can send on behalf of Conference Room B (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>customer.service.lead can send on behalf of General Information (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>support.manager can send on behalf of Technical Support (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>system.admin can send on behalf of No Reply System (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>monitoring.service can send on behalf of System Alerts (<EMAIL>)</div>
</div>
<div class="permission-item">
<div class='permission-description'>svc-backup can send on behalf of Backup Reports (<EMAIL>)</div>
</div>
</div>
</div>
<h2>Cross-Domain Security Analysis</h2>
<p>Analysis of cross-domain permission relationships and associated security risks:</p>
<div class="permission-section" style="border-left: 4px solid #dc3545;">
<h3 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">
Cross-Domain Relationships <span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em;'>8 relationships</span>
</h3>
<p style="color: #666; font-style: italic; margin-bottom: 20px;">Permissions granted across different domains present elevated security risks</p>
<div class="permission-list">
<div class="permission-item" style="border-left: 3px solid #fd7e14;">
<div class='permission-description'>
ðŸŒ <strong>SUBSIDIARY\external.user</strong> (from subsidiary.techcorp.com) has <strong>FullAccess</strong> access to <strong>Shared Project Alpha</strong> (<EMAIL>) in techcorp.com
</div>
<div class='permission-details'>Risk Level: High | Risk Score: 5 | Admin Classification: Business-User-Delegate | Permission Type: Direct | Assignment: Direct Assignment</div>
<span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em; margin-top: 8px;'>High Risk</span>
</div>
<div class="permission-item" style="border-left: 3px solid #ffc107;">
<div class='permission-description'>
ðŸŒ <strong>SUBSIDIARY\vendor.admin</strong> (from subsidiary.techcorp.com) has <strong>FullAccess</strong> access to <strong>Vendor Communications</strong> (<EMAIL>) in techcorp.com
</div>
<div class='permission-details'>Risk Level: Medium | Risk Score: 4 | Admin Classification: Business-User-Delegate | Permission Type: Direct | Assignment: Direct Assignment</div>
<span class='badge' style='background-color: #ffc107; color: white; font-size: 0.8em; margin-top: 8px;'>Medium Risk</span>
</div>
<div class="permission-item" style="border-left: 3px solid #fd7e14;">
<div class='permission-description'>
ðŸŒ <strong>SUBSIDIARY\finance.auditor</strong> (from subsidiary.techcorp.com) has <strong>FullAccess</strong> access to <strong>Finance Team</strong> (<EMAIL>) in techcorp.com
</div>
<div class='permission-details'>Risk Level: High | Risk Score: 6 | Admin Classification: Exchange-Administrator | Permission Type: Direct | Assignment: Direct Assignment</div>
<span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em; margin-top: 8px;'>High Risk</span>
</div>
<div class="permission-item" style="border-left: 3px solid #ffc107;">
<div class='permission-description'>
ðŸŒ <strong>SUBSIDIARY\compliance.manager</strong> (from subsidiary.techcorp.com) has <strong>FullAccess</strong> access to <strong>Compliance Officer</strong> (<EMAIL>) in techcorp.com
</div>
<div class='permission-details'>Risk Level: Medium | Risk Score: 4 | Admin Classification: Read-Only-Administrator | Permission Type: Direct | Assignment: Direct Assignment</div>
<span class='badge' style='background-color: #ffc107; color: white; font-size: 0.8em; margin-top: 8px;'>Medium Risk</span>
</div>
<div class="permission-item" style="border-left: 3px solid #fd7e14;">
<div class='permission-description'>
ðŸŒ <strong>SUBSIDIARY\board.secretary</strong> (from subsidiary.techcorp.com) has <strong>Send-As</strong> access to <strong>Chief Executive Officer</strong> (<EMAIL>) in techcorp.com
</div>
<div class='permission-details'>Risk Level: High | Risk Score: 7 | Admin Classification: Business-User-Delegate | Permission Type: Direct | Assignment: Direct Assignment</div>
<span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em; margin-top: 8px;'>High Risk</span>
</div>
<div class="permission-item" style="border-left: 3px solid #ffc107;">
<div class='permission-description'>
ðŸŒ <strong>SUBSIDIARY\legal.counsel</strong> (from subsidiary.techcorp.com) has <strong>Send-On-Behalf</strong> access to <strong>Legal Department</strong> (<EMAIL>) in techcorp.com
</div>
<div class='permission-details'>Risk Level: Medium | Risk Score: 3 | Admin Classification: Business-User-Delegate | Permission Type: Direct | Assignment: Direct Assignment</div>
<span class='badge' style='background-color: #ffc107; color: white; font-size: 0.8em; margin-top: 8px;'>Medium Risk</span>
</div>
<div class="permission-item" style="border-left: 3px solid #fd7e14;">
<div class='permission-description'>
ðŸŒ <strong>SUBSIDIARY\external.auditor</strong> (from subsidiary.techcorp.com) has <strong>FullAccess</strong> access to <strong>Internal Audit</strong> (<EMAIL>) in techcorp.com
</div>
<div class='permission-details'>Risk Level: High | Risk Score: 6 | Admin Classification: Exchange-Administrator | Permission Type: Direct | Assignment: Direct Assignment</div>
<span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em; margin-top: 8px;'>High Risk</span>
</div>
<div class="permission-item" style="border-left: 3px solid #ffc107;">
<div class='permission-description'>
ðŸŒ <strong>SUBSIDIARY\hr.consultant</strong> (from subsidiary.techcorp.com) has <strong>Send-As</strong> access to <strong>HR Shared Mailbox</strong> (<EMAIL>) in techcorp.com
</div>
<div class='permission-details'>Risk Level: Medium | Risk Score: 4 | Admin Classification: Business-User-Delegate | Permission Type: Direct | Assignment: Direct Assignment</div>
<span class='badge' style='background-color: #ffc107; color: white; font-size: 0.8em; margin-top: 8px;'>Medium Risk</span>
</div>
</div>
</div>
<div class="permission-section" style="border-left: 4px solid #007bff;">
<h3 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px;">Cross-Domain Permission Summary</h3>
<div class="permission-item">
<div class='permission-description'><strong>Domain Relationship:</strong> subsidiary.techcorp.com -> techcorp.com</div>
<div class='permission-details'>Total Permissions: 8 | Admin Domain: subsidiary.techcorp.com | Target Domain: techcorp.com | High Risk Count: 4</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Admin Types:</strong> Business-User-Delegate: 5, Exchange-Administrator: 2, Read-Only-Administrator: 1</div>
</div>
</div>
</div>
<div class="permission-section" style="border-left: 4px solid #ffc107;">
<h3 style="color: #ffc107; border-bottom: 2px solid #ffc107; padding-bottom: 10px;">Risk Assessment Summary</h3>
<div class="dashboard" style="margin-bottom: 20px;">
<div class="card">
<h3>Cross-Domain Impact</h3>
<div class='value' style='color: #dc3545'>8.99%</div>
<p>8 of 89 total relationships</p>
</div>
<div class="card">
<h3>High Risk Relationships</h3>
<div class='value' style='color: #fd7e14'>4</div>
<p>Require immediate attention</p>
</div>
<div class="card">
<h3>Risk Distribution</h3>
<div class='value' style='font-size: 1.5em;'>H:4 M:4 L:81</div>
<p>High | Medium | Low risk levels</p>
</div>
</div>
</div>
<h2>Administrator Discovery & Privilege Analysis</h2>
<p>Comprehensive analysis of Exchange administrative privileges and role assignments:</p>
<div class="permission-section" style="border-left: 4px solid #6f42c1;">
<h3 style="color: #6f42c1; border-bottom: 2px solid #6f42c1; padding-bottom: 10px;">
Exchange Role Assignments <span class='badge' style='background-color: #6f42c1; color: white; font-size: 0.8em;'>6 assignments</span>
</h3>
<p style="color: #666; font-style: italic; margin-bottom: 20px;">Administrative role assignments control Exchange management capabilities</p>
<div class="permission-list">
<div class="permission-item" style="border-left: 3px solid #dc3545;">
<div class='permission-description'>
ðŸ‘¤ <strong>TECHCORP\admin-exchange</strong> assigned to <strong>Organization Management</strong> role
</div>
<div class='permission-details'>Domain: techcorp.com | Type: User | Classification: Exchange-Administrator | Method: Direct | Enabled: True</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Scope:</strong> Scope: Organization-Wide | Domains: All | Organization-Wide: True</div>
<span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em; margin-top: 8px;'>Exchange-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #28a745;">
<div class='permission-description'>
ðŸ‘¤ <strong>TECHCORP\svc-compliance</strong> assigned to <strong>Discovery Management</strong> role
</div>
<div class='permission-details'>Domain: techcorp.com | Type: User | Classification: Read-Only-Administrator | Method: Direct | Enabled: True</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Scope:</strong> Scope: Organization-Wide | Domains: All | Organization-Wide: True</div>
<span class='badge' style='background-color: #28a745; color: white; font-size: 0.8em; margin-top: 8px;'>Read-Only-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #dc3545;">
<div class='permission-description'>
ðŸ‘¤ <strong>TECHCORP\admin-server</strong> assigned to <strong>Server Management</strong> role
</div>
<div class='permission-details'>Domain: techcorp.com | Type: User | Classification: Exchange-Administrator | Method: RoleGroup | Enabled: True</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Scope:</strong> Scope: Organization-Wide | Domains: All | Organization-Wide: True</div>
<span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em; margin-top: 8px;'>Exchange-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #fd7e14;">
<div class='permission-description'>
ðŸ‘¤ <strong>TECHCORP\admin-recipient</strong> assigned to <strong>Recipient Management</strong> role
</div>
<div class='permission-details'>Domain: techcorp.com | Type: User | Classification: Mailbox-Administrator | Method: RoleGroup | Enabled: True</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Scope:</strong> Scope: Organization-Wide | Domains: All | Organization-Wide: True</div>
<span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em; margin-top: 8px;'>Mailbox-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #28a745;">
<div class='permission-description'>
ðŸ‘¤ <strong>SUBSIDIARY\external.auditor</strong> assigned to <strong>View-Only Organization Management</strong> role
</div>
<div class='permission-details'>Domain: subsidiary.techcorp.com | Type: User | Classification: Read-Only-Administrator | Method: Direct | Enabled: True</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Scope:</strong> Scope: Organization-Wide | Domains: All | Organization-Wide: True</div>
<span class='badge' style='background-color: #28a745; color: white; font-size: 0.8em; margin-top: 8px;'>Read-Only-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #dc3545;">
<div class='permission-description'>
ðŸ‘¤ <strong>SUBSIDIARY\finance.auditor</strong> assigned to <strong>Discovery Management</strong> role
</div>
<div class='permission-details'>Domain: subsidiary.techcorp.com | Type: User | Classification: Exchange-Administrator | Method: Direct | Enabled: True</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Scope:</strong> Scope: Custom-Scope | Domains: techcorp.com | Organization-Wide: False</div>
<span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em; margin-top: 8px;'>Exchange-Administrator</span>
</div>
</div>
</div>
<div class="permission-section" style="border-left: 4px solid #17a2b8;">
<h3 style="color: #17a2b8; border-bottom: 2px solid #17a2b8; padding-bottom: 10px;">Administrator Domain Distribution</h3>
<div class="permission-item">
<div class='permission-description'><strong>Domain:</strong> techcorp.com</div>
<div class='permission-details'><strong>Administrator Count:</strong> 4</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Administrators:</strong> TECHCORP\admin-exchange (Exchange-Administrator), TECHCORP\svc-compliance (Read-Only-Administrator), TECHCORP\admin-server (Exchange-Administrator), TECHCORP\admin-recipient (Mailbox-Administrator)</div>
</div>
<div class="permission-item">
<div class='permission-description'><strong>Domain:</strong> subsidiary.techcorp.com</div>
<div class='permission-details'><strong>Administrator Count:</strong> 2</div>
<div class='permission-details' style='margin-top: 5px;'><strong>Administrators:</strong> SUBSIDIARY\external.auditor (Read-Only-Administrator), SUBSIDIARY\finance.auditor (Exchange-Administrator)</div>
</div>
</div>
</div>
<div class="permission-section" style="border-left: 4px solid #dc3545;">
<h3 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">
Cross-Domain Administrative Relationships <span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em;'>1 relationships</span>
</h3>
<p style="color: #666; font-style: italic; margin-bottom: 20px;">Administrative privileges spanning multiple domains present elevated security risks</p>
<div class="permission-list">
<div class="permission-item" style="border-left: 3px solid #fd7e14;">
<div class='permission-description'>
âš ï¸ <strong>subsidiary.techcorp.com</strong> administrators have privileges in <strong>techcorp.com</strong>
</div>
<div class='permission-details'>Relationship Type: Cross-Domain-Administration | Risk Level: High | Administrator Count: 2</div>
<span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em; margin-top: 8px;'>High Risk</span>
</div>
</div>
</div>
<div class="permission-section" style="border-left: 4px solid #28a745;">
<h3 style="color: #28a745; border-bottom: 2px solid #28a745; padding-bottom: 10px;">Administrator Classification Summary</h3>
<div class="dashboard" style="margin-bottom: 20px;">
<div class="card">
<h3>Business-User</h3>
<div class='value' style='color: #6c757d'>2</div>
<p>administrators</p>
</div>
<div class="card">
<h3>Mailbox-Administrator</h3>
<div class='value' style='color: #fd7e14'>1</div>
<p>administrators</p>
</div>
<div class="card">
<h3>Exchange-Administrator</h3>
<div class='value' style='color: #dc3545'>3</div>
<p>administrators</p>
</div>
<div class="card">
<h3>Read-Only-Administrator</h3>
<div class='value' style='color: #28a745'>2</div>
<p>administrators</p>
</div>
</div>
<div class="permission-list">
<div class="permission-item" style="border-left: 3px solid #dc3545;">
<div class='permission-description'>TECHCORP\admin-exchange</div>
<span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em;'>Exchange-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #28a745;">
<div class='permission-description'>TECHCORP\svc-compliance</div>
<span class='badge' style='background-color: #28a745; color: white; font-size: 0.8em;'>Read-Only-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #6c757d;">
<div class='permission-description'>TECHCORP\svc-backup</div>
<span class='badge' style='background-color: #6c757d; color: white; font-size: 0.8em;'>Business-User</span>
</div>
<div class="permission-item" style="border-left: 3px solid #6c757d;">
<div class='permission-description'>TECHCORP\svc-monitoring</div>
<span class='badge' style='background-color: #6c757d; color: white; font-size: 0.8em;'>Business-User</span>
</div>
<div class="permission-item" style="border-left: 3px solid #dc3545;">
<div class='permission-description'>TECHCORP\admin-server</div>
<span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em;'>Exchange-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #fd7e14;">
<div class='permission-description'>TECHCORP\admin-recipient</div>
<span class='badge' style='background-color: #fd7e14; color: white; font-size: 0.8em;'>Mailbox-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #28a745;">
<div class='permission-description'>SUBSIDIARY\external.auditor</div>
<span class='badge' style='background-color: #28a745; color: white; font-size: 0.8em;'>Read-Only-Administrator</span>
</div>
<div class="permission-item" style="border-left: 3px solid #dc3545;">
<div class='permission-description'>SUBSIDIARY\finance.auditor</div>
<span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em;'>Exchange-Administrator</span>
</div>
</div>
</div>
</div>
<div class="footer">
<p><strong>Exchange Mailbox Security Audit Report</strong></p>
<p>Generated by E.Z. Consultancy Exchange Audit Report Generator (Test Version)</p>
<p>Report Generated: 2025-09-14 23:11:12 | Duration: 0.19 seconds</p>
<p>Report ID: 183d2b73-64d5-4a03-a606-84f9d7a05157 | Source Audit ID: b8f3e2d1-4c5a-6789-bcde-f012345678ab</p>
</div>
</div>
</body>
</html>

