﻿{
    "MBX_4_1_SendAsPermissions":  {
                                      "ControlName":  "Send-As Permissions",
                                      "CVSSScore":  7.6,
                                      "AssessmentDate":  "2025-09-14 15:50:20",
                                      "SampleSize":  149,
                                      "CurrentValue":  "0 Send-As permissions found across 149 mailboxes",
                                      "SendAsPermissions":  null,
                                      "TotalSendAsPermissions":  0,
                                      "UniqueUsersWithSendAs":  0,
                                      "Recommendation":  "Regularly review Send-As permissions and ensure they are justified by business requirements",
                                      "BaselineValue":  "Minimal Send-As permissions with business justification",
                                      "Finding":  "Send-As permissions appear reasonable",
                                      "ComplianceScore":  100,
                                      "RiskLevel":  "High",
                                      "ComplianceStatus":  "Compliant",
                                      "ControlID":  "MBX-4.1"
                                  },
    "MBX_5_1_SendOnBehalfPermissions":  {
                                            "ControlName":  "Send-On-Behalf Permissions",
                                            "CVSSScore":  6.2,
                                            "AssessmentDate":  "2025-09-14 15:50:20",
                                            "SampleSize":  149,
                                            "TotalSendOnBehalfPermissions":  53,
                                            "CurrentValue":  "53 Send-On-Behalf permissions found across 149 mailboxes",
                                            "UniqueUsersWithSendOnBehalf":  null,
                                            "SendOnBehalfPermissions":  [
                                                                            {
                                                                                "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Hamad Aloqab"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/CEO Office/Hanan BuHmail"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "Nadwa Group",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/ABG - Groups/Nadwa Group",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/ABG - Groups/Nadwa Group"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "Al Baraka Media Monitor",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Corporate Communications \u0026 PR/Al Baraka Media Monitor",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hawra Mahdi Salman"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "Al Baraka Media Monitor",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Corporate Communications \u0026 PR/Al Baraka Media Monitor",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/Corporate Communications \u0026 PR/Fatima Raoof Al Saleh"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "Mohammed El Qaq",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Mohammed El Qaq",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Shaheen Hassan"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "<EMAIL>",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/ABG - Groups/Info @. ABG User",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/Corporate Communications \u0026 PR/Fatima Raoof Al Saleh"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "<EMAIL>",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/ABG - Groups/Info @. ABG User",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17531031",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/17531031",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Operations/Customers Service Back Office/Mariam Yousif Al Muqla"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17531031",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/17531031",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/CRD Dept/Retail Credit Unit/Aida Adel Abdulla Mohammed"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/Retail Banking \u0026 Branches/Branches/Diplomatic Area/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Ali Mohammed Janahi"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/Retail Banking \u0026 Branches/Branches/Diplomatic Area/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Business Group/Retail Banking \u0026 Branches/Sales/Ali Khalil Al Derzi"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/Retail Banking \u0026 Branches/Branches/Diplomatic Area/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Business Group/Retail Banking \u0026 Branches/Branches/Bahrain Bay/Talal Hamad Fakhri"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Admin Dept/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Eman Hassan Janahi"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17532052",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17532052",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Waleed Isa Abdul Salam"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17532052",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17532052",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/Legal Affairs \u0026 Compliance/Sheema Ismail Haji"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17540544",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/President \u0026 Chief Executive Office/17540544",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Bindu A. Pillai"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17530947",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17530947",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Risk Management/Ali Mohammed Ali Isa"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17530947",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17530947",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Operations/Customers Service Back Office/Aysha Isa Yateem"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17530947",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17530947",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Buthaina J. Dito"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17535084",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/CRD Dept/17535084",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Fatema Ali Hasan Saleh"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17535084",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/CRD Dept/17535084",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Fatima S. Al Alawi"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17535084",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/CRD Dept/17535084",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/CRD Dept/Muneer Abu-Hussaien"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17537552",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/17537552",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/Hussain Mohamed Jaafer"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17537552",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/17537552",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Rehab Ali Shamel"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17537552",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/17537552",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/Aisha Yousif Ahmed Abdulmalik"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17537552",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/17537552",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Dana Sameer Alwazzan"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17537552",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/17537552",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/Omar Al Jowder"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17537552",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Operations/Treasury Back Office/17537552",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17540576",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17540576",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Shaheen Hassan"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17536533",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Operations/17536533",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/Corporate Communications \u0026 PR/Fatima Raoof Al Saleh"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17536533",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Operations/17536533",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Shaheen Hassan"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17537558",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/CEO Office/17537558",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Tahani H. Al Majead"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Ahmed Hussain Jan Ali"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Huthaifa Khaled Ateeq"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/Trade Finance \u0026 Financial Institutions/Anas Saleh El-Arbah"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Raeda Murad"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/CRD Dept/Collection/Nayef Al Kooheji"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/Corporate Banking \u0026 Syndications/Hamza A. Mohammed"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Bushra Jassim"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Yesor Arekat"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABIB/Business Group/International Banking/Trade Finance \u0026 Financial Institutions/Noor Ul Mobeen"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "********",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/********",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Bindu A. Pillai"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17533703",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/IT Dept/17533703",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Tahani H. Al Majead"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17533703",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABIB/IT Dept/17533703",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Tariq Mahmood Kazim"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "Dr. Mohamed Mustapha Khemira",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Strategic Planning/Dr. Mohamed Mustapha Khemira",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Shaheen Hassan"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17910262",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17910262",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "17910911",
                                                                                "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17910911",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "IA-Prj",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/IA-Prj",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/Resigned Staff/AIB-Resigned Staff/Khalid Waheed Abdulrahman"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "IA-Prj",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/IA-Prj",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/IT Project/Ebrahim Al Zayani"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "ABG FIX",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Facility Management/ABG FIX",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/IT Project/ABGHQFMS"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "ABG Board Secretariat",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Corporate Governance \u0026 Board Affairs/ABG Board Secretariat",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/Corporate Governance \u0026 Board Affairs/Abdul Malek Mezher"
                                                                            },
                                                                            {
                                                                                "MailboxDisplayName":  "Houssem Ben Haj Amor",
                                                                                "MailboxIdentity":  "baraka.com/alBaraka/ABG/Business Development and Investments/Houssem Ben Haj Amor",
                                                                                "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                "PermissionType":  "Send-On-Behalf",
                                                                                "DelegateUser":  "baraka.com/alBaraka/ABG/Business Development and Investments/Ana Maria Fandarac"
                                                                            }
                                                                        ],
                                            "Recommendation":  "Regularly review Send-On-Behalf permissions and ensure they align with current business needs",
                                            "BaselineValue":  "Minimal Send-On-Behalf permissions with business justification",
                                            "Finding":  "High number of Send-On-Behalf permissions - review required",
                                            "ComplianceScore":  25,
                                            "RiskLevel":  "Medium",
                                            "ComplianceStatus":  "Review Required",
                                            "ControlID":  "MBX-5.1"
                                        },
    "AuditMetadata":  {
                          "PowerShellEdition":  "Desktop",
                          "ScriptAuthor":  "E.Z. Consultancy",
                          "ScriptReleaseDate":  "September 11, 2025",
                          "PowerShellVersion":  "5.1.14393.8244",
                          "ScriptVersion":  "1.6.7",
                          "FilteredDomains":  "albaraka.com",
                          "AuditUser":  "ad21bar",
                          "AssessmentScope":  "5 Critical Mailbox Security Controls",
                          "MaxMailboxSample":  500,
                          "OrganizationName":  "barakaonline",
                          "DomainFilterScope":  "Domain-specific audit for: albaraka.com",
                          "DomainFilterEnabled":  true,
                          "AuditID":  "50387a1f-b1f9-4ea9-b252-f91fb4e5a145",
                          "ExchangeServers":  3,
                          "ComputerName":  "BARAKAEXCH02",
                          "TotalMailboxes":  149,
                          "AuditStartTime":  "2025-09-14 15:50:20"
                      },
    "MBX_1_1_ImpersonationRights":  {
                                        "ControlName":  "Mailbox Impersonation Rights",
                                        "ImpersonationUsers":  [
                                                                   {
                                                                       "RoleAssigneeType":  "RoleGroup",
                                                                       "WhenCreated":  "2009-12-24 13:44:23",
                                                                       "IsValid":  true,
                                                                       "AssignmentMethod":  "Direct",
                                                                       "RoleAssignee":  "baraka.com/Microsoft Exchange Security Groups/Organization Management",
                                                                       "RoleAssigneeName":  "Organization Management"
                                                                   },
                                                                   {
                                                                       "RoleAssigneeType":  "RoleGroup",
                                                                       "WhenCreated":  "2009-12-24 13:44:23",
                                                                       "IsValid":  true,
                                                                       "AssignmentMethod":  "Direct",
                                                                       "RoleAssignee":  "baraka.com/Microsoft Exchange Security Groups/Hygiene Management",
                                                                       "RoleAssigneeName":  "Hygiene Management"
                                                                   },
                                                                   {
                                                                       "RoleAssigneeType":  "User",
                                                                       "WhenCreated":  "2016-04-14 12:23:17",
                                                                       "IsValid":  true,
                                                                       "AssignmentMethod":  "Direct",
                                                                       "RoleAssignee":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner",
                                                                       "RoleAssigneeName":  "Meeting Planner"
                                                                   },
                                                                   {
                                                                       "RoleAssigneeType":  "User",
                                                                       "WhenCreated":  "2017-02-02 10:44:17",
                                                                       "IsValid":  true,
                                                                       "AssignmentMethod":  "Direct",
                                                                       "RoleAssignee":  "baraka.com/alBaraka/ABG/Service Accounts/exchvoicemail",
                                                                       "RoleAssigneeName":  "exchvoicemail"
                                                                   },
                                                                   {
                                                                       "RoleAssigneeType":  "RoleGroup",
                                                                       "WhenCreated":  "2023-03-18 09:59:50",
                                                                       "IsValid":  true,
                                                                       "AssignmentMethod":  "Direct",
                                                                       "RoleAssignee":  "baraka.com/Microsoft Exchange Security Groups/CVE-2023-23397-Script",
                                                                       "RoleAssigneeName":  "CVE-2023-23397-Script"
                                                                   }
                                                               ],
                                        "AssessmentDate":  "2025-09-14 15:50:20",
                                        "TotalImpersonationAssignments":  5,
                                        "CurrentValue":  "5 accounts with ApplicationImpersonation rights",
                                        "CVSSScore":  9.3,
                                        "Recommendation":  "Regularly review ApplicationImpersonation role assignments and remove unnecessary permissions",
                                        "BaselineValue":  "Minimal impersonation rights with regular review",
                                        "Finding":  "High number of impersonation rights - security risk",
                                        "ComplianceScore":  25,
                                        "RiskLevel":  "Critical",
                                        "ComplianceStatus":  "Review Required",
                                        "ControlID":  "MBX-1.1"
                                    },
    "MBX_2_1_FullAccessPermissions":  {
                                          "ControlName":  "Mailbox Full Access Permissions",
                                          "CVSSScore":  8.1,
                                          "AssessmentDate":  "2025-09-14 15:50:20",
                                          "SampleSize":  149,
                                          "TotalFullAccessPermissions":  1110,
                                          "CurrentValue":  "1110 Full Access permissions found across 149 mailboxes",
                                          "CrossDomainAnalysis":  {
                                                                      "Status":  "Skipped",
                                                                      "Reason":  "Insufficient data or administrator discovery failed"
                                                                  },
                                          "FullAccessPermissions":  [
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Meeting Planner",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Meeting Planner",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Meeting Planner",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Meeting Planner",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Meeting Planner",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Meeting Planner",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Meeting Planner",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Mohsin Dashti",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Mohsin Dashti"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohsin Dashti",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Mohsin Dashti"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohsin Dashti",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Mohsin Dashti"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohsin Dashti",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Mohsin Dashti"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohsin Dashti",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Mohsin Dashti"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohsin Dashti",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Mohsin Dashti"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohsin Dashti",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Mohsin Dashti"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\s.sayyar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\hbuhmail",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\Haloqab",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Hamad A. Al Oqab",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Abdulla Talal Al Qannas",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Abdulla Talal A. Qannas"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Talal Al Qannas",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Abdulla Talal A. Qannas"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Talal Al Qannas",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Abdulla Talal A. Qannas"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Talal Al Qannas",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Abdulla Talal A. Qannas"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Talal Al Qannas",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Abdulla Talal A. Qannas"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Talal Al Qannas",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Abdulla Talal A. Qannas"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Talal Al Qannas",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Abdulla Talal A. Qannas"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Abdulla Al Abdulmohsen",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Al Abdulmohsen",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Al Abdulmohsen",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Al Abdulmohsen",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Al Abdulmohsen",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Al Abdulmohsen",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulla Al Abdulmohsen",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Mohamed A. Al Rowaiei",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohamed A. Al Rowaiei"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed A. Al Rowaiei",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohamed A. Al Rowaiei"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed A. Al Rowaiei",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohamed A. Al Rowaiei"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed A. Al Rowaiei",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohamed A. Al Rowaiei"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed A. Al Rowaiei",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohamed A. Al Rowaiei"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed A. Al Rowaiei",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohamed A. Al Rowaiei"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed A. Al Rowaiei",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohamed A. Al Rowaiei"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Saleh Al Yousef",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Board/Saleh Al Yousef"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Saleh Al Yousef",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Board/Saleh Al Yousef"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Saleh Al Yousef",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Board/Saleh Al Yousef"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Saleh Al Yousef",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Board/Saleh Al Yousef"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Saleh Al Yousef",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Board/Saleh Al Yousef"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Saleh Al Yousef",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Board/Saleh Al Yousef"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Saleh Al Yousef",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Board/Saleh Al Yousef"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Mohamed Abdulaziz Jamsheer",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/IT Project/Mohamed Abdulaziz Jamsheer"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed Abdulaziz Jamsheer",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/IT Project/Mohamed Abdulaziz Jamsheer"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed Abdulaziz Jamsheer",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/IT Project/Mohamed Abdulaziz Jamsheer"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed Abdulaziz Jamsheer",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/IT Project/Mohamed Abdulaziz Jamsheer"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed Abdulaziz Jamsheer",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/IT Project/Mohamed Abdulaziz Jamsheer"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed Abdulaziz Jamsheer",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/IT Project/Mohamed Abdulaziz Jamsheer"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohamed Abdulaziz Jamsheer",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/IT Project/Mohamed Abdulaziz Jamsheer"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Ahmed M. AbdulGhaffar",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ahmed M. AbdulGhaffar",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ahmed M. AbdulGhaffar",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ahmed M. AbdulGhaffar",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ahmed M. AbdulGhaffar",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ahmed M. AbdulGhaffar",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ahmed M. AbdulGhaffar",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Mohammed Alawi Hassan Al-Alawi",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/Mohammed Alawi Hassan Al-Alawi"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohammed Alawi Hassan Al-Alawi",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/Mohammed Alawi Hassan Al-Alawi"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohammed Alawi Hassan Al-Alawi",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/Mohammed Alawi Hassan Al-Alawi"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohammed Alawi Hassan Al-Alawi",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/Mohammed Alawi Hassan Al-Alawi"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohammed Alawi Hassan Al-Alawi",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/Mohammed Alawi Hassan Al-Alawi"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohammed Alawi Hassan Al-Alawi",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/Mohammed Alawi Hassan Al-Alawi"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Mohammed Alawi Hassan Al-Alawi",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Internal Audit/Mohammed Alawi Hassan Al-Alawi"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Fadhel Almadhoob",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fadel A. Al-Madhoob"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fadhel Almadhoob",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fadel A. Al-Madhoob"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fadhel Almadhoob",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fadel A. Al-Madhoob"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fadhel Almadhoob",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fadel A. Al-Madhoob"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fadhel Almadhoob",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fadel A. Al-Madhoob"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fadhel Almadhoob",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fadel A. Al-Madhoob"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fadhel Almadhoob",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fadel A. Al-Madhoob"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Ali Asgar Mandasorwala",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Ali Asgar Mandasorwala"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ali Asgar Mandasorwala",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Ali Asgar Mandasorwala"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ali Asgar Mandasorwala",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Ali Asgar Mandasorwala"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ali Asgar Mandasorwala",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Ali Asgar Mandasorwala"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ali Asgar Mandasorwala",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Ali Asgar Mandasorwala"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ali Asgar Mandasorwala",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Ali Asgar Mandasorwala"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Ali Asgar Mandasorwala",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Ali Asgar Mandasorwala"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Fatima A. Mohammed",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Fatima A. Mohammed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fatima A. Mohammed",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Fatima A. Mohammed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fatima A. Mohammed",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Fatima A. Mohammed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fatima A. Mohammed",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Fatima A. Mohammed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fatima A. Mohammed",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Fatima A. Mohammed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fatima A. Mohammed",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Fatima A. Mohammed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad14bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Fatima A. Mohammed",
                                                                            "MailboxIdentity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Fatima A. Mohammed"
                                                                        },
                                                                        {
                                                                            "Deny":  "False",
                                                                            "User":  "BARAKA\\gtbadmin",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Abdulrahman Shawqi Albinmohamed",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulrahman Shawqi Albinmohamed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BUILTIN\\Administrators",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulrahman Shawqi Albinmohamed",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulrahman Shawqi Albinmohamed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\ad04bar",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulrahman Shawqi Albinmohamed",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulrahman Shawqi Albinmohamed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Domain Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulrahman Shawqi Albinmohamed",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulrahman Shawqi Albinmohamed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Enterprise Admins",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulrahman Shawqi Albinmohamed",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulrahman Shawqi Albinmohamed"
                                                                        },
                                                                        {
                                                                            "Deny":  "True",
                                                                            "User":  "BARAKA\\Organization Management",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  true,
                                                                            "MailboxDisplayName":  "Abdulrahman Shawqi Albinmohamed",
                                                                            "MailboxIdentity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulrahman Shawqi Albinmohamed"
                                                                        }
                                                                    ],
                                          "UniqueUsersWithFullAccess":  null,
                                          "Recommendation":  "Regularly review Full Access permissions and remove unnecessary delegations. Consider using Send-As or Send-On-Behalf instead.",
                                          "BaselineValue":  "Minimal Full Access delegations with business justification",
                                          "Finding":  "High number of Full Access permissions - review required",
                                          "ComplianceScore":  25,
                                          "RiskLevel":  "High",
                                          "ComplianceStatus":  "Review Required",
                                          "ControlID":  "MBX-2.1"
                                      },
    "MBX_3_1_AuditLogging":  {
                                 "ControlName":  "Mailbox Audit Logging Configuration",
                                 "CVSSScore":  6.8,
                                 "MailboxAuditBypassUsers":  [
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 "",
                                                                 ""
                                                             ],
                                 "SampleSize":  50,
                                 "AssessmentDate":  "2025-09-14 15:50:20",
                                 "AuditEnabledMailboxes":  0,
                                 "CurrentValue":  "Admin audit: True, Mailbox audit enabled: 0 of 50 sampled",
                                 "AdminAuditLogEnabled":  true,
                                 "SampleMailboxAuditSettings":  [
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Meeting Planner",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Service Accounts/Meeting Planner"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Mohsin Dashti",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Mohsin Dashti"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Hamad A. Al Oqab",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hamad A. Al Oqab"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Abdulla Talal Al Qannas",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Abdulla Talal A. Qannas"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Abdulla Al Abdulmohsen",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla A. Almohsen"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Mohamed A. Al Rowaiei",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohamed A. Al Rowaiei"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Saleh Al Yousef",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Board/Saleh Al Yousef"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Mohamed Abdulaziz Jamsheer",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/IT Project/Mohamed Abdulaziz Jamsheer"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Ahmed M. AbdulGhaffar",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Investors Relations/Ahmed M. AbdulGhaffar"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Mohammed Alawi Hassan Al-Alawi",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Internal Audit/Mohammed Alawi Hassan Al-Alawi"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Fadhel Almadhoob",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fadel A. Al-Madhoob"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Ali Asgar Mandasorwala",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Ali Asgar Mandasorwala"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Fatima A. Mohammed",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Fatima A. Mohammed"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Abdulrahman Shawqi Albinmohamed",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulrahman Shawqi Albinmohamed"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Hassan Y. Al Banna",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hassan Y. Al Banna"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Ebrahim Al Zayani",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/IT Project/Ebrahim Al Zayani"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Dr. Hala Radwan",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hala Khalid Radwan"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Eman Isa Ali",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Internal Audit/Eman Isa Ali"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Mohammed Abdul Latif Al-Mahmood",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Sharia Internal Audit/Mohammed Abdul Latif Al-Mahmood"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Fouad Janahi",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fouad Janahi"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Shaheen Hassan",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Shaheen Hassan"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Husain A. Ebrahim",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Internal Audit/Husain A. Isa Ebrahim"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Dr. Hassan Al-Aali",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hassan A. Al-Aali"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Dr. El Tigani El Tayeb Mohammed",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Strategic Planning/Dr. El Tigani El Tayeb Mohammed"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Nadwa Group",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/ABG - Groups/Nadwa Group"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Al Baraka Media Monitor",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Corporate Communications \u0026 PR/Al Baraka Media Monitor"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Dr. Abdul Sattar Abu Ghudah",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Sharia Board/Dr. Abdul Sattar Abu Ghudah"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Sheema Ismail Haji",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Legal Affairs \u0026 Compliance/Sheema Ismail Haji"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Bindu Pillai",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Bindu A. Pillai"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Al Baraka IT",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/IT Project/Al Baraka IT"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Waleed Isa Abdul Salam",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Waleed Isa Abdul Salam"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Mohamed Jasim Alebrahim",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Mohamed Jasim Alebrahim"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Mohammed El Qaq",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Mohammed El Qaq"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Usha Ramesh",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Usha Ramesh"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Fatima Raoof Al Saleh",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Corporate Communications \u0026 PR/Fatima Raoof Al Saleh"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Abdulla Ahmed Buaneq",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulla Ahmed Buaneq"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Nadwa Invite",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/ABG - Groups/Nadwa Invite"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Ameera Abdulrahman Al Meer",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Ameera Abdulrahman Al Meer"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Fatima Ahmed Shehab Ahmed",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Fatima Ahmed Shehab Ahmed"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Mobile Application",
                                                                        "Identity":  "baraka.com/alBaraka Special Accounts/ABG/Service Accounts/Mobile Application"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Jilani Benlagha",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/ABG - Consultants/Jilani Benlagha"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Abdulsamad Isa Khalfan",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Abdulsamad Isa Khalfan"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Hawra Mahdi Salman",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/Hawra Mahdi Salman"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "<EMAIL>",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/ABG - Groups/Info @. ABG User"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Abdul Elah Sabbahi",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Board/Abdul Elah Sabbahi"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "17537553",
                                                                        "Identity":  "baraka.com/alBaraka/ABIB/IT Dept/17537553"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "ABGHQFMS",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/IT Project/ABGHQFMS"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "17531074",
                                                                        "Identity":  "baraka.com/Resigned Staff/ABG-Resigned Staff/17531074"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "17530414",
                                                                        "Identity":  "baraka.com/alBaraka/ABIB/Admin Dept/17530414"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Aldana Alammadi",
                                                                        "Identity":  "baraka.com/alBaraka/ABG/Financial Control Dept./Aldana Alammadi"
                                                                    }
                                                                ],
                                 "AuditBypassCount":  0,
                                 "Recommendation":  "Ensure mailbox audit is enabled for all mailboxes",
                                 "BaselineValue":  "Admin and mailbox audit logging enabled organization-wide",
                                 "Finding":  "Admin audit logging enabled",
                                 "ComplianceScore":  75,
                                 "RiskLevel":  "Medium",
                                 "ComplianceStatus":  "Review Required",
                                 "ControlID":  "MBX-3.1"
                             },
    "ComplianceSummary":  {
                              "SuccessfulAssessments":  5,
                              "PowerShellVersion":  "5.1.14393.8244",
                              "FrameworkVersion":  "Exchange Mailbox Security Controls v1.0",
                              "AssessmentType":  "Exchange Server Mailbox Security Controls Audit",
                              "AuditDurationSeconds":  1679.65,
                              "AuditExecutedBy":  "ad21bar",
                              "AuditID":  "50387a1f-b1f9-4ea9-b252-f91fb4e5a145",
                              "ComputerName":  "BARAKAEXCH02",
                              "FailedAssessments":  0,
                              "AuditEndTime":  "2025-09-14 16:18:20",
                              "TotalControlsAssessed":  5,
                              "AuditType":  "Read-Only Assessment - Production Safe",
                              "AuditStartTime":  "2025-09-14 15:50:20"
                          },
    "AdministratorDiscovery":  {
                                   "CrossDomainRelationships":  {

                                                                },
                                   "AdminDomainMap":  {

                                                      },
                                   "RoleAssignments":  {

                                                       },
                                   "AdminClassification":  {

                                                           }
                               }
}
