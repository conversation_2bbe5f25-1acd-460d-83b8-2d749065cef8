## **🔐 5 MOST CRITICAL PAM AUDIT QUESTIONS**

### **1. ACCESS CONTROL & AUTHORIZATION**

**❓ CRITICAL QUESTION:**
> **"Can you demonstrate that all privileged access assignments follow the principle of least privilege with documented business justification, and show me the complete inventory of who currently has administrative rights across all systems?"**

**🎯 Why Critical:**
- Excessive privileged access is the #1 attack vector for data breaches
- Undocumented or unjustified admin rights create compliance violations
- Lack of comprehensive inventory means unknown security exposure

**📋 Evidence to Examine:**
- **Privileged Account Inventory**: Complete list with role assignments, scope, and business justification
- **Access Request Forms**: Documentation showing approval workflow and business need
- **Role-Based Access Control (RBAC) Matrix**: Mapping of roles to specific privileges
- **Exception Reports**: Accounts with elevated privileges beyond standard roles
- **Cross-Domain Analysis**: Privileged access spanning multiple domains/systems

---

### **2. MONITORING & LOGGING**

**❓ CRITICAL QUESTION:**
> **"Show me real-time monitoring of all privileged account activities, including session recordings, command logging, and automated alerting for suspicious behaviors - and demonstrate how you investigate and respond to privilege escalation attempts."**

**🎯 Why Critical:**
- Privileged accounts are prime targets for insider threats and external attackers
- Regulatory frameworks (SOX, PCI-DSS, HIPAA) mandate privileged access monitoring
- Without comprehensive logging, security incidents go undetected

**📋 Evidence to Examine:**
- **Session Recording Systems**: Video/keystroke capture of privileged sessions
- **Command Logging**: Detailed audit trails of administrative actions
- **Real-Time Alerting**: SIEM rules for privilege abuse detection
- **Behavioral Analytics**: Baseline vs. anomalous privileged user behavior
- **Incident Response Logs**: Evidence of investigating privilege-related security events

---

### **3. ACCOUNT LIFECYCLE MANAGEMENT**

**❓ CRITICAL QUESTION:**
> **"Walk me through your complete privileged account lifecycle process from provisioning to deprovisioning, and show me evidence that orphaned, dormant, or terminated user accounts with administrative privileges have been identified and removed."**

**🎯 Why Critical:**
- Orphaned privileged accounts are "golden tickets" for attackers
- Dormant admin accounts bypass normal security controls
- Poor lifecycle management creates compliance audit findings

**📋 Evidence to Examine:**
- **Provisioning Workflows**: Automated processes with approval gates
- **Regular Access Reviews**: Quarterly/annual certification of privileged access
- **Dormant Account Reports**: Accounts unused for 90+ days with admin rights
- **Termination Procedures**: Immediate revocation of privileges upon employee departure
- **Service Account Management**: Lifecycle processes for non-human privileged accounts

---

### **4. SECURITY CONTROLS & COMPLIANCE**

**❓ CRITICAL QUESTION:**
> **"Demonstrate your technical controls for privileged access including multi-factor authentication, password vaulting, session isolation, and show me your compliance mapping to regulatory requirements like SOX Section 404, PCI-DSS Requirement 7, and ISO 27001 A.9.2."**

**🎯 Why Critical:**
- Technical controls are the last line of defense against privilege abuse
- Regulatory compliance failures result in fines and audit findings
- Weak technical controls enable lateral movement and data exfiltration

**📋 Evidence to Examine:**
- **Multi-Factor Authentication (MFA)**: Enforcement for all privileged accounts
- **Privileged Access Management (PAM) Solution**: Password vaulting and session management
- **Network Segmentation**: Isolation of privileged access pathways
- **Compliance Mapping**: Documentation showing control alignment to regulations
- **Vulnerability Assessments**: Regular testing of privileged access security controls

---

### **5. RISK ASSESSMENT & GOVERNANCE**

**❓ CRITICAL QUESTION:**
> **"Show me your privileged access risk assessment methodology, including how you identify and mitigate cross-domain administrative relationships, and demonstrate executive oversight and accountability for privileged access security decisions."**

**🎯 Why Critical:**
- Cross-domain privileges create exponential risk exposure
- Executive accountability ensures adequate investment in PAM security
- Risk-based approach prioritizes remediation of highest-impact vulnerabilities

**📋 Evidence to Examine:**
- **Risk Assessment Framework**: Methodology for scoring privileged access risks
- **Cross-Domain Analysis**: Mapping of administrative relationships across domains/forests
- **Executive Reporting**: Regular briefings to C-level on privileged access risks
- **Risk Register**: Documented privileged access risks with mitigation plans
- **Governance Committee**: Board/executive oversight of privileged access decisions

---

## **🚨 HIGH-RISK GAP INDICATORS**

When evaluating responses, these findings indicate **CRITICAL** security gaps:

### **🔴 IMMEDIATE ATTENTION REQUIRED:**
- **No comprehensive privileged account inventory**
- **Privileged accounts without MFA enforcement**
- **No session recording or command logging**
- **Dormant admin accounts older than 90 days**
- **Cross-domain administrative privileges without business justification**
- **No executive oversight or risk assessment process**

### **🟡 SIGNIFICANT CONCERNS:**
- **Manual provisioning/deprovisioning processes**
- **Shared privileged accounts without individual accountability**
- **Privileged access reviews conducted annually or less frequently**
- **No automated alerting for privilege escalation attempts**
- **Compliance gaps in regulatory control mapping**

### **📊 AUDIT SCORING FRAMEWORK:**

| Control Area | Weight | Critical Gaps | Score Impact |
|--------------|--------|---------------|--------------|
| Access Control & Authorization | 25% | No inventory, excessive privileges | -50 points |
| Monitoring & Logging | 25% | No session recording, no alerting | -50 points |
| Account Lifecycle Management | 20% | Orphaned accounts, manual processes | -40 points |
| Security Controls & Compliance | 20% | No MFA, no PAM solution | -40 points |
| Risk Assessment & Governance | 10% | No risk assessment, no oversight | -20 points |

**Overall PAM Security Score = 100 - (Sum of Gap Penalties)**

These questions will help identify the most critical privileged access security gaps that pose the highest risk to organizational security and compliance posture.
