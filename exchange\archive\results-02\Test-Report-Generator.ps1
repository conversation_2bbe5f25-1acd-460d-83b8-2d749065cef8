#Requires -Version 5.1

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.1.0 (Enhanced Version)
# **Exchange Version:** Exchange Server 2016/2019/Online Compatible
# 🏛️ **Authority:** Internal Audit
# ================================================================================

<#
================================================================================
Exchange Mailbox Security Audit Report Generator (Enhanced Version)
================================================================================
Description: Enhanced version with improved visual formatting, detailed permission
analysis, and comprehensive reporting features while maintaining parsing stability.
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$JsonFilePath = "",

    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "",

    [Parameter(Mandatory = $false)]
    [switch]$ConsoleOutput
)

# Initialize report generation session
$ReportStartTime = Get-Date
$ReportID = [System.Guid]::NewGuid()

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Mailbox Security Audit Report Generator (Test Version)" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Report ID: $ReportID" -ForegroundColor Yellow
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

# ================================================================================
# HELPER FUNCTIONS
# ================================================================================

function Find-LatestJsonFile {
    param([string]$SearchPath = ".")
    
    try {
        $JsonFiles = Get-ChildItem -Path $SearchPath -Filter "*.json" -ErrorAction SilentlyContinue |
            Where-Object { $_.Name -like "*Exchange*" -or $_.Name -like "*audit*" -or $_.Name -like "*Sample*" } |
            Sort-Object LastWriteTime -Descending
        
        if ($JsonFiles -and $JsonFiles.Count -gt 0) {
            return $JsonFiles[0].FullName
        }
        return $null
    }
    catch {
        Write-Host "[ERROR] Failed to search for JSON files: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function ConvertTo-SafeHtml {
    param([string]$Text)
    
    if ([string]::IsNullOrWhiteSpace($Text)) {
        return "N/A"
    }
    
    # Simple HTML encoding
    $Text = $Text -replace "&", "&amp;"
    $Text = $Text -replace "<", "&lt;"
    $Text = $Text -replace ">", "&gt;"
    $Text = $Text -replace '"', "&quot;"
    $Text = $Text -replace "'", "&#39;"
    
    return $Text
}

# ================================================================================
# INPUT VALIDATION AND FILE PROCESSING
# ================================================================================

Write-Host "Step 1/3: Validating input parameters and locating JSON file..." -ForegroundColor Yellow

# Determine JSON file path
if ([string]::IsNullOrWhiteSpace($JsonFilePath)) {
    Write-Host "  No JSON file specified, searching for latest audit results..." -ForegroundColor Gray
    $JsonFilePath = Find-LatestJsonFile
    
    if ([string]::IsNullOrWhiteSpace($JsonFilePath)) {
        Write-Host "[ERROR] No JSON audit files found in current directory." -ForegroundColor Red
        Write-Host "Please specify a JSON file path using -JsonFilePath parameter." -ForegroundColor Red
        exit 1
    }
    
    Write-Host "  Found JSON file: $JsonFilePath" -ForegroundColor Green
}

# Validate JSON file exists
if (-not (Test-Path -Path $JsonFilePath)) {
    Write-Host "[ERROR] JSON file not found: $JsonFilePath" -ForegroundColor Red
    exit 1
}

Write-Host "  JSON file validated: $JsonFilePath" -ForegroundColor Green

# Determine output path
if ([string]::IsNullOrWhiteSpace($OutputPath)) {
    $JsonFileName = [System.IO.Path]::GetFileNameWithoutExtension($JsonFilePath)
    $OutputPath = ".\$JsonFileName-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').html"
}

Write-Host "  Output path: $OutputPath" -ForegroundColor Green

# ================================================================================
# JSON DATA LOADING AND VALIDATION
# ================================================================================

Write-Host "Step 2/3: Loading and validating JSON audit data..." -ForegroundColor Yellow

try {
    $JsonContent = Get-Content -Path $JsonFilePath -Raw -Encoding UTF8
    $AuditData = $JsonContent | ConvertFrom-Json -ErrorAction Stop
    
    Write-Host "  JSON data loaded successfully" -ForegroundColor Green
    
    # Extract key sections
    $AuditMetadata = if ($AuditData.AuditMetadata) { $AuditData.AuditMetadata } else { @{} }
    $ComplianceSummary = if ($AuditData.ComplianceSummary) { $AuditData.ComplianceSummary } else { @{} }
    
    Write-Host "  Audit ID: $($AuditMetadata.AuditID)" -ForegroundColor Gray
    Write-Host "  Organization: $($AuditMetadata.OrganizationName)" -ForegroundColor Gray
    
    # Validate key sections exist
    $ControlSections = @(
        "MBX_1_1_ImpersonationRights",
        "MBX_2_1_FullAccessPermissions", 
        "MBX_3_1_AuditLogging",
        "MBX_4_1_SendAsPermissions",
        "MBX_5_1_SendOnBehalfPermissions"
    )
    
    $FoundSections = @()
    $ErrorSections = @()
    
    foreach ($Section in $ControlSections) {
        if ($AuditData.$Section) {
            if ($AuditData.$Section.Error) {
                $ErrorSections += $Section
            } else {
                $FoundSections += $Section
            }
        }
    }
    
    Write-Host "  Found control sections: $($FoundSections.Count)" -ForegroundColor Green
    Write-Host "  Error sections: $($ErrorSections.Count)" -ForegroundColor $(if ($ErrorSections.Count -gt 0) { "Yellow" } else { "Green" })
    
    if ($AuditData.AdministratorDiscovery) {
        Write-Host "  Administrator discovery data: Available" -ForegroundColor Green
    } else {
        Write-Host "  Administrator discovery data: Not available" -ForegroundColor Yellow
    }
    
}
catch {
    Write-Host "[ERROR] Failed to load or parse JSON file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ================================================================================
# SIMPLE REPORT GENERATION
# ================================================================================

Write-Host "Step 3/3: Generating simple HTML report..." -ForegroundColor Yellow

# Process control data
$ControlSummaryData = @()

# Define control mappings
$ControlMappings = @{
    "MBX_1_1_ImpersonationRights" = @{ ID = "MBX-1.1"; Name = "Mailbox Impersonation Rights"; Category = "Access Control" }
    "MBX_2_1_FullAccessPermissions" = @{ ID = "MBX-2.1"; Name = "Full Access Permissions"; Category = "Delegation Management" }
    "MBX_3_1_AuditLogging" = @{ ID = "MBX-3.1"; Name = "Audit Logging Configuration"; Category = "Compliance & Monitoring" }
    "MBX_4_1_SendAsPermissions" = @{ ID = "MBX-4.1"; Name = "Send-As Permissions"; Category = "Email Delegation" }
    "MBX_5_1_SendOnBehalfPermissions" = @{ ID = "MBX-5.1"; Name = "Send-On-Behalf Permissions"; Category = "Email Delegation" }
}

foreach ($ControlKey in $ControlMappings.Keys) {
    $ControlData = $AuditData.$ControlKey
    $Mapping = $ControlMappings[$ControlKey]
    
    if ($ControlData) {
        $ControlSummaryData += [PSCustomObject]@{
            ControlID = if ($ControlData.ControlID) { $ControlData.ControlID } else { $Mapping.ID }
            ControlName = if ($ControlData.ControlName) { $ControlData.ControlName } else { $Mapping.Name }
            Category = $Mapping.Category
            RiskLevel = if ($ControlData.RiskLevel) { $ControlData.RiskLevel } else { "Unknown" }
            ComplianceStatus = if ($ControlData.ComplianceStatus) { $ControlData.ComplianceStatus } else { "Unknown" }
            ComplianceScore = if ($ControlData.ComplianceScore) { $ControlData.ComplianceScore } else { 0 }
            CVSSScore = if ($ControlData.CVSSScore) { $ControlData.CVSSScore } else { 0 }
            Finding = if ($ControlData.Finding) { $ControlData.Finding } else { "No findings available" }
            Recommendation = if ($ControlData.Recommendation) { $ControlData.Recommendation } else { "No recommendations available" }
            HasError = if ($ControlData.Error) { $true } else { $false }
            ErrorMessage = if ($ControlData.Error) { $ControlData.Error } else { "" }
        }
    }
}

# Calculate dashboard metrics
$TotalControls = $ControlSummaryData.Count
$CompliantControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count
$ReviewRequiredControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Review Required" }).Count
$NonCompliantControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" }).Count
$ErrorControls = ($ControlSummaryData | Where-Object { $_.HasError }).Count

$OverallComplianceScore = if ($TotalControls -gt 0) { 
    [math]::Round(($ControlSummaryData | Measure-Object -Property ComplianceScore -Average).Average, 1) 
} else { 0 }

# Risk level distribution
$CriticalRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Critical" }).Count
$HighRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "High" }).Count
$MediumRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Medium" }).Count
$LowRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Low" }).Count

Write-Host "  Dashboard metrics calculated:" -ForegroundColor Green
Write-Host "    - Total Controls: $TotalControls" -ForegroundColor Gray
Write-Host "    - Compliant: $CompliantControls" -ForegroundColor Gray
Write-Host "    - Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor Gray

# Generate simple HTML using StringBuilder approach
$Html = New-Object System.Text.StringBuilder

$null = $Html.AppendLine('<!DOCTYPE html>')
$null = $Html.AppendLine('<html lang="en">')
$null = $Html.AppendLine('<head>')
$null = $Html.AppendLine('<meta charset="UTF-8">')
$null = $Html.AppendLine('<meta name="viewport" content="width=device-width, initial-scale=1.0">')
$null = $Html.AppendLine("<title>Exchange Mailbox Security Audit Report - $($AuditMetadata.OrganizationName)</title>")
$null = $Html.AppendLine('<style>')
$null = $Html.AppendLine('body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }')
$null = $Html.AppendLine('.container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }')
$null = $Html.AppendLine('.header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; }')
$null = $Html.AppendLine('.header h1 { margin: 0 0 10px 0; font-size: 2.5em; }')
$null = $Html.AppendLine('.dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }')
$null = $Html.AppendLine('.card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-left: 4px solid #667eea; }')
$null = $Html.AppendLine('.card h3 { margin: 0 0 15px 0; color: #667eea; }')
$null = $Html.AppendLine('.card .value { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }')
$null = $Html.AppendLine('table { width: 100%; border-collapse: collapse; margin: 20px 0; }')
$null = $Html.AppendLine('th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }')
$null = $Html.AppendLine('th { background-color: #f8f9fa; font-weight: bold; }')
$null = $Html.AppendLine('tr:hover { background-color: #f5f5f5; }')
$null = $Html.AppendLine('.badge { padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold; color: white; }')
$null = $Html.AppendLine('.risk-critical { background-color: #dc3545; }')
$null = $Html.AppendLine('.risk-high { background-color: #fd7e14; }')
$null = $Html.AppendLine('.risk-medium { background-color: #ffc107; color: #212529; }')
$null = $Html.AppendLine('.risk-low { background-color: #28a745; }')
$null = $Html.AppendLine('.status-compliant { background-color: #28a745; }')
$null = $Html.AppendLine('.status-review { background-color: #fd7e14; }')
$null = $Html.AppendLine('.status-non-compliant { background-color: #dc3545; }')
$null = $Html.AppendLine('.footer { text-align: center; margin-top: 40px; padding: 20px; border-top: 1px solid #ddd; color: #666; }')
$null = $Html.AppendLine('')
$null = $Html.AppendLine('/* Permission Analysis Sections */')
$null = $Html.AppendLine('.permission-section { margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; }')
$null = $Html.AppendLine('.permission-list { margin-top: 15px; }')
$null = $Html.AppendLine('.permission-item { background: white; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 3px solid #e9ecef; }')
$null = $Html.AppendLine('.permission-description { font-weight: bold; color: #333; margin-bottom: 8px; }')
$null = $Html.AppendLine('.dept-info { font-size: 0.9em; margin: 5px 0; padding: 5px 10px; border-radius: 4px; }')
$null = $Html.AppendLine('.dept-info.same-dept { background: #d4edda; color: #155724; }')
$null = $Html.AppendLine('.dept-info.cross-dept { background: #f8d7da; color: #721c24; }')
$null = $Html.AppendLine('.risk-flag { background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; font-weight: bold; margin-left: 10px; }')
$null = $Html.AppendLine('.permission-details { font-size: 0.85em; color: #666; margin-top: 8px; font-style: italic; }

/* Cross-Domain Analysis Styles */
.cross-domain-section { border-left: 4px solid #dc3545 !important; }
.cross-domain-item { border-left: 3px solid #dc3545 !important; }
.cross-domain-badge { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }

/* Administrator Discovery Styles */
.admin-section { border-left: 4px solid #6f42c1 !important; }
.admin-item { border-left: 3px solid #6f42c1 !important; }
.admin-badge { background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%); }

/* Enhanced Risk Indicators */
.risk-critical-bg { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
.risk-high-bg { background: linear-gradient(135deg, #fd7e14 0%, #e8590c 100%); }
.risk-medium-bg { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); }
.risk-low-bg { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }

/* Enhanced Permission Item Styling */
.permission-item:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.15); transition: all 0.3s ease; }
.permission-description { font-size: 1.1em; line-height: 1.4; }

/* Domain Relationship Styling */
.domain-relationship { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: 1px solid #dee2e6; }
.cross-domain-warning { background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); border: 1px solid #ffeaa7; }
.cross-domain-danger { background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border: 1px solid #f5c6cb; }

/* Administrator Classification Colors */
.admin-exchange { color: #dc3545; font-weight: bold; }
.admin-mailbox { color: #fd7e14; font-weight: bold; }
.admin-readonly { color: #28a745; font-weight: bold; }
.admin-business { color: #6c757d; font-weight: bold; }')
$null = $Html.AppendLine('</style>')
$null = $Html.AppendLine('</head>')
$null = $Html.AppendLine('<body>')
$null = $Html.AppendLine('<div class="container">')

# Header
$null = $Html.AppendLine('<div class="header">')
$null = $Html.AppendLine('<h1>Exchange Mailbox Security Audit Report</h1>')
$null = $Html.AppendLine('<p>Comprehensive Security Assessment and Compliance Analysis</p>')
$null = $Html.AppendLine("<p><strong>Organization:</strong> $($AuditMetadata.OrganizationName) | <strong>Audit Date:</strong> $($AuditMetadata.AuditStartTime) | <strong>Audit ID:</strong> $($AuditMetadata.AuditID)</p>")
$null = $Html.AppendLine('</div>')

# Dashboard
$null = $Html.AppendLine('<div class="dashboard">')
$null = $Html.AppendLine('<div class="card">')
$null = $Html.AppendLine('<h3>Overall Compliance Score</h3>')
$null = $Html.AppendLine("<div class='value' style='color: $(if ($OverallComplianceScore -ge 80) { '#28a745' } elseif ($OverallComplianceScore -ge 60) { '#ffc107' } else { '#dc3545' })'>$OverallComplianceScore%</div>")
$null = $Html.AppendLine('<p>Average compliance across all controls</p>')
$null = $Html.AppendLine('</div>')

$null = $Html.AppendLine('<div class="card">')
$null = $Html.AppendLine('<h3>Controls Assessment</h3>')
$null = $Html.AppendLine("<div class='value'>$TotalControls</div>")
$ControlsText = "Compliant: $CompliantControls | Review Required: $ReviewRequiredControls | Non-Compliant: $NonCompliantControls"
$null = $Html.AppendLine("<p>$ControlsText</p>")
$null = $Html.AppendLine('</div>')

$null = $Html.AppendLine('<div class="card">')
$null = $Html.AppendLine('<h3>Risk Distribution</h3>')
$RiskColor = if ($CriticalRiskControls -gt 0 -or $HighRiskControls -gt 0) { '#dc3545' } elseif ($MediumRiskControls -gt 0) { '#ffc107' } else { '#28a745' }
$RiskLevel = if ($CriticalRiskControls -gt 0) { 'CRITICAL' } elseif ($HighRiskControls -gt 0) { 'HIGH' } elseif ($MediumRiskControls -gt 0) { 'MEDIUM' } else { 'LOW' }
$null = $Html.AppendLine("<div class='value' style='color: $RiskColor'>$RiskLevel</div>")
$RiskText = "Critical: $CriticalRiskControls | High: $HighRiskControls | Medium: $MediumRiskControls | Low: $LowRiskControls"
$null = $Html.AppendLine("<p>$RiskText</p>")
$null = $Html.AppendLine('</div>')
$null = $Html.AppendLine('</div>')

# Control Summary Table
$null = $Html.AppendLine('<h2>Security Control Analysis</h2>')
$null = $Html.AppendLine('<table>')
$null = $Html.AppendLine('<thead>')
$null = $Html.AppendLine('<tr><th>Control ID</th><th>Control Name</th><th>Risk Level</th><th>Compliance Status</th><th>Score</th><th>Finding</th></tr>')
$null = $Html.AppendLine('</thead>')
$null = $Html.AppendLine('<tbody>')

foreach ($Control in $ControlSummaryData) {
    $StatusIcon = switch ($Control.ComplianceStatus.ToLower()) {
        "compliant" { "OK" }
        "review required" { "WARN" }
        "non-compliant" { "FAIL" }
        default { "UNK" }
    }

    $RiskClass = "risk-" + $Control.RiskLevel.ToLower()
    $StatusClass = "status-" + $Control.ComplianceStatus.ToLower().Replace(' ', '-')

    $SafeControlID = ConvertTo-SafeHtml -Text $Control.ControlID
    $SafeCategory = ConvertTo-SafeHtml -Text $Control.Category
    $SafeControlName = ConvertTo-SafeHtml -Text $Control.ControlName
    $SafeFinding = ConvertTo-SafeHtml -Text $Control.Finding

    $ErrorText = ""
    if ($Control.HasError) {
        $SafeErrorMessage = ConvertTo-SafeHtml -Text $Control.ErrorMessage
        $ErrorText = "<br><small style='color: red;'>ERROR: $SafeErrorMessage</small>"
    }

    $null = $Html.AppendLine('<tr>')
    $null = $Html.AppendLine("<td><strong>$SafeCategory</strong><br><small>$SafeControlID</small></td>")
    $null = $Html.AppendLine("<td>$SafeControlName$ErrorText</td>")
    $null = $Html.AppendLine("<td><span class='badge $RiskClass'>$($Control.RiskLevel)</span></td>")
    $null = $Html.AppendLine("<td>$StatusIcon <span class='badge $StatusClass'>$($Control.ComplianceStatus)</span></td>")
    $null = $Html.AppendLine("<td><strong>$($Control.ComplianceScore)%</strong></td>")
    $null = $Html.AppendLine("<td>$SafeFinding</td>")
    $null = $Html.AppendLine('</tr>')
}

$null = $Html.AppendLine('</tbody>')
$null = $Html.AppendLine('</table>')

# ================================================================================
# DETAILED PERMISSION ANALYSIS SECTIONS
# ================================================================================

$null = $Html.AppendLine('<h2>Detailed Permission Analysis</h2>')
$null = $Html.AppendLine('<p>The following sections provide human-readable details of specific permission relationships identified during the audit:</p>')

# Process each permission type
$PermissionSections = @(
    @{
        Title = "Impersonation Rights"
        DataKey = "MBX_1_1_ImpersonationRights"
        PermissionKey = "ImpersonationRights"
        Description = "Users with application impersonation rights can access mailboxes on behalf of other users"
        RiskLevel = "Critical"
    },
    @{
        Title = "Full Access Permissions"
        DataKey = "MBX_2_1_FullAccessPermissions"
        PermissionKey = "FullAccessPermissions"
        Description = "Users with full access can read, modify, and delete items in target mailboxes"
        RiskLevel = "High"
    },
    @{
        Title = "Send-As Permissions"
        DataKey = "MBX_4_1_SendAsPermissions"
        PermissionKey = "SendAsPermissions"
        Description = "Users with Send-As permissions can send emails appearing to come from the target mailbox"
        RiskLevel = "High"
    },
    @{
        Title = "Send-On-Behalf Permissions"
        DataKey = "MBX_5_1_SendOnBehalfPermissions"
        PermissionKey = "SendOnBehalfPermissions"
        Description = "Users with Send-On-Behalf permissions can send emails on behalf of the target mailbox owner"
        RiskLevel = "Medium"
    }
)

foreach ($Section in $PermissionSections) {
    if ($AuditData.PSObject.Properties.Name -contains $Section.DataKey) {
        $SectionData = $AuditData.($Section.DataKey)
        $Permissions = $SectionData.($Section.PermissionKey)

        if ($Permissions -and $Permissions.Count -gt 0) {
            # Section Header
            $RiskColor = switch ($Section.RiskLevel.ToLower()) {
                "critical" { "#dc3545" }
                "high" { "#fd7e14" }
                "medium" { "#ffc107" }
                "low" { "#28a745" }
                default { "#6c757d" }
            }

            $null = $Html.AppendLine('<div class="permission-section">')
            $null = $Html.AppendLine("<h3 style='color: $RiskColor; border-bottom: 2px solid $RiskColor; padding-bottom: 10px;'>")
            $null = $Html.AppendLine("$($Section.Title) <span class='badge' style='background-color: $RiskColor; color: white; font-size: 0.8em;'>$($Permissions.Count) permissions</span>")
            $null = $Html.AppendLine('</h3>')
            $null = $Html.AppendLine("<p style='color: #666; font-style: italic; margin-bottom: 20px;'>$($Section.Description)</p>")

            # Permission List
            $null = $Html.AppendLine('<div class="permission-list">')

            foreach ($Permission in $Permissions) {
                # Determine risk indicators
                $RiskIndicators = @()
                $CrossDepartment = $false

                if ($Permission.PSObject.Properties.Name -contains "UserDepartment" -and
                    $Permission.PSObject.Properties.Name -contains "Department" -and
                    $Permission.UserDepartment -ne $Permission.Department) {
                    $CrossDepartment = $true
                    $RiskIndicators += "Cross-Department"
                }

                if ($Permission.PSObject.Properties.Name -contains "IsInherited" -and $Permission.IsInherited -eq $false) {
                    $RiskIndicators += "Direct Assignment"
                }

                if ($Permission.PSObject.Properties.Name -contains "BusinessJustification" -and
                    [string]::IsNullOrWhiteSpace($Permission.BusinessJustification)) {
                    $RiskIndicators += "No Business Justification"
                }

                # Build human-readable description - Fixed for Send-On-Behalf permissions
                $UserName = ""
                $UserEmail = "N/A"

                # Handle different permission types with proper field mapping
                switch ($Section.Title) {
                    "Send-On-Behalf Permissions" {
                        # Use DelegateUser field for Send-On-Behalf permissions
                        $UserName = if ($Permission.PSObject.Properties.Name -contains "DelegateUserDisplayName") {
                            $Permission.DelegateUserDisplayName
                        } elseif ($Permission.PSObject.Properties.Name -contains "DelegateUser") {
                            $Permission.DelegateUser -replace ".*\\", ""
                        } else {
                            "Unknown User"
                        }
                    }
                    default {
                        # Use standard User fields for other permission types
                        $UserName = if ($Permission.PSObject.Properties.Name -contains "UserDisplayName") {
                            $Permission.UserDisplayName
                        } elseif ($Permission.PSObject.Properties.Name -contains "User") {
                            $Permission.User -replace ".*\\", ""
                        } else {
                            "Unknown User"
                        }
                        $UserEmail = if ($Permission.PSObject.Properties.Name -contains "UserPrimarySmtpAddress") {
                            $Permission.UserPrimarySmtpAddress
                        } else {
                            "N/A"
                        }
                    }
                }

                $MailboxName = $Permission.MailboxDisplayName
                $MailboxEmail = $Permission.MailboxPrimarySmtpAddress

                $PermissionDescription = switch ($Section.Title) {
                    "Impersonation Rights" { "$UserName has impersonation rights on $MailboxName ($MailboxEmail)" }
                    "Full Access Permissions" { "$UserName has full access to $MailboxName ($MailboxEmail)" }
                    "Send-As Permissions" { "$UserName can send as $MailboxName ($MailboxEmail)" }
                    "Send-On-Behalf Permissions" { "$UserName can send on behalf of $MailboxName ($MailboxEmail)" }
                    default { "$UserName has $($Permission.AccessRights) permission on $MailboxName ($MailboxEmail)" }
                }

                # Risk indicator styling
                $RiskClass = if ($CrossDepartment) { "cross-dept-risk" } else { "same-dept" }

                $null = $Html.AppendLine('<div class="permission-item">')
                $null = $Html.AppendLine("<div class='permission-description'>$PermissionDescription</div>")

                # Department information
                if ($Permission.PSObject.Properties.Name -contains "UserDepartment" -and
                    $Permission.PSObject.Properties.Name -contains "Department") {
                    $DeptInfo = "User Department: $($Permission.UserDepartment) | Target Department: $($Permission.Department)"
                    if ($CrossDepartment) {
                        $null = $Html.AppendLine("<div class='dept-info cross-dept'>$DeptInfo <span class='risk-flag'>CROSS-DEPARTMENT</span></div>")
                    } else {
                        $null = $Html.AppendLine("<div class='dept-info same-dept'>$DeptInfo</div>")
                    }
                }

                # Additional details
                $Details = @()
                if ($Permission.PSObject.Properties.Name -contains "AssignmentDate") {
                    $Details += "Assigned: $($Permission.AssignmentDate)"
                }
                if ($Permission.PSObject.Properties.Name -contains "LastAccessDate") {
                    $Details += "Last Access: $($Permission.LastAccessDate)"
                }
                if ($Permission.PSObject.Properties.Name -contains "BusinessJustification" -and
                    -not [string]::IsNullOrWhiteSpace($Permission.BusinessJustification)) {
                    $Details += "Justification: $($Permission.BusinessJustification)"
                }

                if ($Details.Count -gt 0) {
                    $null = $Html.AppendLine("<div class='permission-details'>$($Details -join ' | ')</div>")
                }

                $null = $Html.AppendLine('</div>')
            }

            $null = $Html.AppendLine('</div>')
            $null = $Html.AppendLine('</div>')
        }
    }
}

# ================================================================================
# CROSS-DOMAIN ANALYSIS SECTION
# ================================================================================

# Process CrossDomainAnalysis from MBX_2_1_FullAccessPermissions
if ($AuditData.PSObject.Properties.Name -contains "MBX_2_1_FullAccessPermissions" -and
    $AuditData.MBX_2_1_FullAccessPermissions.PSObject.Properties.Name -contains "CrossDomainAnalysis") {

    $CrossDomainData = $AuditData.MBX_2_1_FullAccessPermissions.CrossDomainAnalysis

    $null = $Html.AppendLine('<h2>Cross-Domain Security Analysis</h2>')
    $null = $Html.AppendLine('<p>Analysis of cross-domain permission relationships and associated security risks:</p>')

    # Cross-Domain Relationships
    if ($CrossDomainData.PSObject.Properties.Name -contains "CrossDomainRelationships" -and
        $CrossDomainData.CrossDomainRelationships.Count -gt 0) {

        $null = $Html.AppendLine('<div class="permission-section" style="border-left: 4px solid #dc3545;">')
        $null = $Html.AppendLine('<h3 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">')
        $null = $Html.AppendLine("Cross-Domain Relationships <span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em;'>$($CrossDomainData.CrossDomainRelationships.Count) relationships</span>")
        $null = $Html.AppendLine('</h3>')
        $null = $Html.AppendLine('<p style="color: #666; font-style: italic; margin-bottom: 20px;">Permissions granted across different domains present elevated security risks</p>')

        $null = $Html.AppendLine('<div class="permission-list">')

        foreach ($Relationship in $CrossDomainData.CrossDomainRelationships) {
            $RiskColor = switch ($Relationship.RiskLevel.ToLower()) {
                "critical" { "#dc3545" }
                "high" { "#fd7e14" }
                "medium" { "#ffc107" }
                "low" { "#28a745" }
                default { "#6c757d" }
            }

            $null = $Html.AppendLine('<div class="permission-item" style="border-left: 3px solid ' + $RiskColor + ';">')
            $null = $Html.AppendLine("<div class='permission-description'>")
            $null = $Html.AppendLine("🌐 <strong>$($Relationship.AdminUser)</strong> (from $($Relationship.AdminDomain)) has <strong>$($Relationship.AccessRights)</strong> access to <strong>$($Relationship.MailboxDisplayName)</strong> ($($Relationship.MailboxPrimarySmtpAddress)) in $($Relationship.MailboxDomain)")
            $null = $Html.AppendLine("</div>")

            # Risk and classification details
            $CrossDomainDetails = @()
            $CrossDomainDetails += "Risk Level: $($Relationship.RiskLevel)"
            $CrossDomainDetails += "Risk Score: $($Relationship.RiskScore)"
            $CrossDomainDetails += "Admin Classification: $($Relationship.AdminClassification)"
            $CrossDomainDetails += "Permission Type: $($Relationship.PermissionType)"
            if ($Relationship.PSObject.Properties.Name -contains "IsInherited") {
                $InheritanceStatus = if ($Relationship.IsInherited) { "Inherited" } else { "Direct Assignment" }
                $CrossDomainDetails += "Assignment: $InheritanceStatus"
            }

            $null = $Html.AppendLine("<div class='permission-details'>$($CrossDomainDetails -join ' | ')</div>")
            $null = $Html.AppendLine("<span class='badge' style='background-color: $RiskColor; color: white; font-size: 0.8em; margin-top: 8px;'>$($Relationship.RiskLevel) Risk</span>")
            $null = $Html.AppendLine('</div>')
        }

        $null = $Html.AppendLine('</div>')
        $null = $Html.AppendLine('</div>')
    }

    # Permission Summary
    if ($CrossDomainData.PSObject.Properties.Name -contains "PermissionSummary") {
        $null = $Html.AppendLine('<div class="permission-section" style="border-left: 4px solid #007bff;">')
        $null = $Html.AppendLine('<h3 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px;">Cross-Domain Permission Summary</h3>')

        foreach ($SummaryKey in $CrossDomainData.PermissionSummary.PSObject.Properties.Name) {
            $Summary = $CrossDomainData.PermissionSummary.$SummaryKey

            $null = $Html.AppendLine('<div class="permission-item">')
            $null = $Html.AppendLine("<div class='permission-description'><strong>Domain Relationship:</strong> $SummaryKey</div>")

            $SummaryDetails = @()
            $SummaryDetails += "Total Permissions: $($Summary.Count)"
            $SummaryDetails += "Admin Domain: $($Summary.AdminDomain)"
            $SummaryDetails += "Target Domain: $($Summary.MailboxDomain)"
            $SummaryDetails += "High Risk Count: $($Summary.HighRiskCount)"

            $null = $Html.AppendLine("<div class='permission-details'>$($SummaryDetails -join ' | ')</div>")

            # Admin Types breakdown
            if ($Summary.PSObject.Properties.Name -contains "AdminTypes") {
                $AdminTypesList = @()
                foreach ($AdminType in $Summary.AdminTypes.PSObject.Properties.Name) {
                    $AdminTypesList += "$AdminType`: $($Summary.AdminTypes.$AdminType)"
                }
                $null = $Html.AppendLine("<div class='permission-details' style='margin-top: 5px;'><strong>Admin Types:</strong> $($AdminTypesList -join ', ')</div>")
            }

            $null = $Html.AppendLine('</div>')
        }

        $null = $Html.AppendLine('</div>')
        $null = $Html.AppendLine('</div>')
    }

    # Risk Assessment
    if ($CrossDomainData.PSObject.Properties.Name -contains "RiskAssessment") {
        $RiskAssessment = $CrossDomainData.RiskAssessment

        $null = $Html.AppendLine('<div class="permission-section" style="border-left: 4px solid #ffc107;">')
        $null = $Html.AppendLine('<h3 style="color: #ffc107; border-bottom: 2px solid #ffc107; padding-bottom: 10px;">Risk Assessment Summary</h3>')

        $null = $Html.AppendLine('<div class="dashboard" style="margin-bottom: 20px;">')

        # Risk metrics cards
        $null = $Html.AppendLine('<div class="card">')
        $null = $Html.AppendLine('<h3>Cross-Domain Impact</h3>')
        $null = $Html.AppendLine("<div class='value' style='color: #dc3545'>$($RiskAssessment.CrossDomainPercentage)%</div>")
        $null = $Html.AppendLine("<p>$($RiskAssessment.CrossDomainRelationships) of $($RiskAssessment.TotalPermissionRelationships) total relationships</p>")
        $null = $Html.AppendLine('</div>')

        $null = $Html.AppendLine('<div class="card">')
        $null = $Html.AppendLine('<h3>High Risk Relationships</h3>')
        $null = $Html.AppendLine("<div class='value' style='color: #fd7e14'>$($RiskAssessment.HighRiskRelationships)</div>")
        $null = $Html.AppendLine('<p>Require immediate attention</p>')
        $null = $Html.AppendLine('</div>')

        if ($RiskAssessment.PSObject.Properties.Name -contains "RiskDistribution") {
            $null = $Html.AppendLine('<div class="card">')
            $null = $Html.AppendLine('<h3>Risk Distribution</h3>')
            $null = $Html.AppendLine("<div class='value' style='font-size: 1.5em;'>H:$($RiskAssessment.RiskDistribution.High) M:$($RiskAssessment.RiskDistribution.Medium) L:$($RiskAssessment.RiskDistribution.Low)</div>")
            $null = $Html.AppendLine('<p>High | Medium | Low risk levels</p>')
            $null = $Html.AppendLine('</div>')
        }

        $null = $Html.AppendLine('</div>')
        $null = $Html.AppendLine('</div>')
    }
}

# ================================================================================
# ADMINISTRATOR DISCOVERY SECTION
# ================================================================================

# Process AdministratorDiscovery section
if ($AuditData.PSObject.Properties.Name -contains "AdministratorDiscovery") {
    $AdminData = $AuditData.AdministratorDiscovery

    $null = $Html.AppendLine('<h2>Administrator Discovery & Privilege Analysis</h2>')
    $null = $Html.AppendLine('<p>Comprehensive analysis of Exchange administrative privileges and role assignments:</p>')

    # Role Assignments
    if ($AdminData.PSObject.Properties.Name -contains "RoleAssignments" -and
        $AdminData.RoleAssignments.Count -gt 0) {

        $null = $Html.AppendLine('<div class="permission-section" style="border-left: 4px solid #6f42c1;">')
        $null = $Html.AppendLine('<h3 style="color: #6f42c1; border-bottom: 2px solid #6f42c1; padding-bottom: 10px;">')
        $null = $Html.AppendLine("Exchange Role Assignments <span class='badge' style='background-color: #6f42c1; color: white; font-size: 0.8em;'>$($AdminData.RoleAssignments.Count) assignments</span>")
        $null = $Html.AppendLine('</h3>')
        $null = $Html.AppendLine('<p style="color: #666; font-style: italic; margin-bottom: 20px;">Administrative role assignments control Exchange management capabilities</p>')

        $null = $Html.AppendLine('<div class="permission-list">')

        foreach ($Assignment in $AdminData.RoleAssignments) {
            $AdminColor = switch ($Assignment.AdminClassification) {
                "Exchange-Administrator" { "#dc3545" }
                "Mailbox-Administrator" { "#fd7e14" }
                "Read-Only-Administrator" { "#28a745" }
                default { "#6c757d" }
            }

            $null = $Html.AppendLine('<div class="permission-item" style="border-left: 3px solid ' + $AdminColor + ';">')
            $null = $Html.AppendLine("<div class='permission-description'>")
            $null = $Html.AppendLine("👤 <strong>$($Assignment.RoleAssignee)</strong> assigned to <strong>$($Assignment.Role)</strong> role")
            $null = $Html.AppendLine("</div>")

            # Assignment details
            $AssignmentDetails = @()
            $AssignmentDetails += "Domain: $($Assignment.AssigneeDomain)"
            $AssignmentDetails += "Type: $($Assignment.AssigneeType)"
            $AssignmentDetails += "Classification: $($Assignment.AdminClassification)"
            $AssignmentDetails += "Method: $($Assignment.AssignmentMethod)"
            $AssignmentDetails += "Enabled: $($Assignment.IsEnabled)"

            $null = $Html.AppendLine("<div class='permission-details'>$($AssignmentDetails -join ' | ')</div>")

            # Scope information
            if ($Assignment.PSObject.Properties.Name -contains "Scope") {
                $ScopeDetails = @()
                $ScopeDetails += "Scope: $($Assignment.Scope.Type)"
                if ($Assignment.Scope.PSObject.Properties.Name -contains "AffectedDomains") {
                    $ScopeDetails += "Domains: $($Assignment.Scope.AffectedDomains -join ', ')"
                }
                $ScopeDetails += "Organization-Wide: $($Assignment.Scope.IsOrganizationWide)"

                $null = $Html.AppendLine("<div class='permission-details' style='margin-top: 5px;'><strong>Scope:</strong> $($ScopeDetails -join ' | ')</div>")
            }

            $null = $Html.AppendLine("<span class='badge' style='background-color: $AdminColor; color: white; font-size: 0.8em; margin-top: 8px;'>$($Assignment.AdminClassification)</span>")
            $null = $Html.AppendLine('</div>')
        }

        $null = $Html.AppendLine('</div>')
        $null = $Html.AppendLine('</div>')
    }

    # Administrator Domain Mapping
    if ($AdminData.PSObject.Properties.Name -contains "AdminDomainMap") {
        $null = $Html.AppendLine('<div class="permission-section" style="border-left: 4px solid #17a2b8;">')
        $null = $Html.AppendLine('<h3 style="color: #17a2b8; border-bottom: 2px solid #17a2b8; padding-bottom: 10px;">Administrator Domain Distribution</h3>')

        foreach ($Domain in $AdminData.AdminDomainMap.PSObject.Properties.Name) {
            $Admins = $AdminData.AdminDomainMap.$Domain

            $null = $Html.AppendLine('<div class="permission-item">')
            $null = $Html.AppendLine("<div class='permission-description'><strong>Domain:</strong> $Domain</div>")
            $null = $Html.AppendLine("<div class='permission-details'><strong>Administrator Count:</strong> $($Admins.Count)</div>")

            $AdminList = @()
            foreach ($Admin in $Admins) {
                $AdminList += "$($Admin.RoleAssignee) ($($Admin.AdminClassification))"
            }

            $null = $Html.AppendLine("<div class='permission-details' style='margin-top: 5px;'><strong>Administrators:</strong> $($AdminList -join ', ')</div>")
            $null = $Html.AppendLine('</div>')
        }

        $null = $Html.AppendLine('</div>')
        $null = $Html.AppendLine('</div>')
    }

    # Cross-Domain Administrative Relationships
    if ($AdminData.PSObject.Properties.Name -contains "CrossDomainRelationships" -and
        $AdminData.CrossDomainRelationships.Count -gt 0) {

        $null = $Html.AppendLine('<div class="permission-section" style="border-left: 4px solid #dc3545;">')
        $null = $Html.AppendLine('<h3 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px;">')
        $null = $Html.AppendLine("Cross-Domain Administrative Relationships <span class='badge' style='background-color: #dc3545; color: white; font-size: 0.8em;'>$($AdminData.CrossDomainRelationships.Count) relationships</span>")
        $null = $Html.AppendLine('</h3>')
        $null = $Html.AppendLine('<p style="color: #666; font-style: italic; margin-bottom: 20px;">Administrative privileges spanning multiple domains present elevated security risks</p>')

        $null = $Html.AppendLine('<div class="permission-list">')

        foreach ($Relationship in $AdminData.CrossDomainRelationships) {
            $RiskColor = switch ($Relationship.RiskLevel.ToLower()) {
                "critical" { "#dc3545" }
                "high" { "#fd7e14" }
                "medium" { "#ffc107" }
                "low" { "#28a745" }
                default { "#6c757d" }
            }

            $null = $Html.AppendLine('<div class="permission-item" style="border-left: 3px solid ' + $RiskColor + ';">')
            $null = $Html.AppendLine("<div class='permission-description'>")
            $null = $Html.AppendLine("⚠️ <strong>$($Relationship.AdminDomain)</strong> administrators have privileges in <strong>$($Relationship.TargetDomain)</strong>")
            $null = $Html.AppendLine("</div>")

            $AdminRelationshipDetails = @()
            $AdminRelationshipDetails += "Relationship Type: $($Relationship.RelationshipType)"
            $AdminRelationshipDetails += "Risk Level: $($Relationship.RiskLevel)"
            $AdminRelationshipDetails += "Administrator Count: $($Relationship.AdminCount)"

            $null = $Html.AppendLine("<div class='permission-details'>$($AdminRelationshipDetails -join ' | ')</div>")
            $null = $Html.AppendLine("<span class='badge' style='background-color: $RiskColor; color: white; font-size: 0.8em; margin-top: 8px;'>$($Relationship.RiskLevel) Risk</span>")
            $null = $Html.AppendLine('</div>')
        }

        $null = $Html.AppendLine('</div>')
        $null = $Html.AppendLine('</div>')
    }

    # Administrator Classification Summary
    if ($AdminData.PSObject.Properties.Name -contains "AdminClassification") {
        $null = $Html.AppendLine('<div class="permission-section" style="border-left: 4px solid #28a745;">')
        $null = $Html.AppendLine('<h3 style="color: #28a745; border-bottom: 2px solid #28a745; padding-bottom: 10px;">Administrator Classification Summary</h3>')

        # Calculate classification counts
        $ClassificationCounts = @{}
        foreach ($Admin in $AdminData.AdminClassification.PSObject.Properties.Name) {
            $Classification = $AdminData.AdminClassification.$Admin
            if ($ClassificationCounts.ContainsKey($Classification)) {
                $ClassificationCounts[$Classification]++
            } else {
                $ClassificationCounts[$Classification] = 1
            }
        }

        # Display classification summary
        $null = $Html.AppendLine('<div class="dashboard" style="margin-bottom: 20px;">')

        foreach ($Classification in $ClassificationCounts.Keys) {
            $ClassColor = switch ($Classification) {
                "Exchange-Administrator" { "#dc3545" }
                "Mailbox-Administrator" { "#fd7e14" }
                "Read-Only-Administrator" { "#28a745" }
                "Business-User" { "#6c757d" }
                default { "#007bff" }
            }

            $null = $Html.AppendLine('<div class="card">')
            $null = $Html.AppendLine("<h3>$Classification</h3>")
            $null = $Html.AppendLine("<div class='value' style='color: $ClassColor'>$($ClassificationCounts[$Classification])</div>")
            $null = $Html.AppendLine('<p>administrators</p>')
            $null = $Html.AppendLine('</div>')
        }

        $null = $Html.AppendLine('</div>')

        # Individual administrator listings
        $null = $Html.AppendLine('<div class="permission-list">')
        foreach ($Admin in $AdminData.AdminClassification.PSObject.Properties.Name) {
            $Classification = $AdminData.AdminClassification.$Admin
            $ClassColor = switch ($Classification) {
                "Exchange-Administrator" { "#dc3545" }
                "Mailbox-Administrator" { "#fd7e14" }
                "Read-Only-Administrator" { "#28a745" }
                "Business-User" { "#6c757d" }
                default { "#007bff" }
            }

            $null = $Html.AppendLine('<div class="permission-item" style="border-left: 3px solid ' + $ClassColor + ';">')
            $null = $Html.AppendLine("<div class='permission-description'>$Admin</div>")
            $null = $Html.AppendLine("<span class='badge' style='background-color: $ClassColor; color: white; font-size: 0.8em;'>$Classification</span>")
            $null = $Html.AppendLine('</div>')
        }
        $null = $Html.AppendLine('</div>')

        $null = $Html.AppendLine('</div>')
        $null = $Html.AppendLine('</div>')
    }
}

# Footer
$ReportEndTime = Get-Date
$ReportDuration = $ReportEndTime - $ReportStartTime

$null = $Html.AppendLine('<div class="footer">')
$null = $Html.AppendLine('<p><strong>Exchange Mailbox Security Audit Report</strong></p>')
$null = $Html.AppendLine('<p>Generated by E.Z. Consultancy Exchange Audit Report Generator (Test Version)</p>')
$null = $Html.AppendLine("<p>Report Generated: $($ReportEndTime.ToString('yyyy-MM-dd HH:mm:ss')) | Duration: $([math]::Round($ReportDuration.TotalSeconds, 2)) seconds</p>")
$null = $Html.AppendLine("<p>Report ID: $ReportID | Source Audit ID: $($AuditMetadata.AuditID)</p>")
$null = $Html.AppendLine('</div>')

$null = $Html.AppendLine('</div>')
$null = $Html.AppendLine('</body>')
$null = $Html.AppendLine('</html>')

# Save HTML report
try {
    $Html.ToString() | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
    Write-Host "  HTML report generated successfully: $OutputPath" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to generate HTML report: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Console output if requested
if ($ConsoleOutput) {
    Write-Host ""
    Write-Host "=== CONSOLE SUMMARY ===" -ForegroundColor Yellow
    Write-Host "Organization: $($AuditMetadata.OrganizationName)" -ForegroundColor White
    Write-Host "Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor $(if ($OverallComplianceScore -ge 80) { "Green" } elseif ($OverallComplianceScore -ge 60) { "Yellow" } else { "Red" })
    Write-Host "Total Controls: $TotalControls | Compliant: $CompliantControls | Review Required: $ReviewRequiredControls" -ForegroundColor White
    Write-Host "Risk Distribution: Critical: $CriticalRiskControls | High: $HighRiskControls | Medium: $MediumRiskControls | Low: $LowRiskControls" -ForegroundColor White
    Write-Host "========================" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Report Generation Completed Successfully" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "HTML Report: $OutputPath" -ForegroundColor Cyan
Write-Host "Report ID: $ReportID" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green

# Launch browser
try {
    Write-Host ""
    Write-Host "Opening report in default web browser..." -ForegroundColor Yellow
    Start-Process $OutputPath
}
catch {
    Write-Host "[WARNING] Could not open browser automatically: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Please manually open: $OutputPath" -ForegroundColor Yellow
}
