# Exchange Mailbox Security Audit JSON to TXT Converter

## Overview
The `jsn-to-txt.ps1` script is a comprehensive PowerShell tool that converts Exchange mailbox security audit JSON output files into detailed human-readable text reports. This script is designed to work with the JSON output generated by `Exchange-Mailbox-Security-Audit.ps1` version 1.6.7.

## Features

### ✅ **Comprehensive Text Report Generation**
- **Executive Summary**: Overall compliance scores and risk assessments
- **Complete Permission Listings**: All permissions/rights found (not just samples)
- **Detailed Metadata**: Full user details, assignment dates, business justifications
- **Professional Formatting**: Well-structured sections with clear headers and data presentation
- **Enhanced Control Details**: Comprehensive analysis for each security control (MBX-1.1 through MBX-5.1)

### ✅ **Production-Ready Features**
- **Self-Contained**: No dependencies on the original audit script
- **Error Handling**: Comprehensive error handling for file operations
- **Meaningful File Names**: Includes timestamps and audit IDs for organization
- **UTF-8 Encoding**: Ensures proper character handling across different systems

## Usage

### **Basic Usage**
```powershell
# Convert using default paths
.\jsn-to-txt.ps1

# Convert specific JSON file
.\jsn-to-txt.ps1 -JsonFilePath "C:\Audits\Exchange-Results.json"

# Specify custom output directory
.\jsn-to-txt.ps1 -JsonFilePath "audit.json" -OutputDirectory "C:\Reports"

# Disable timestamp in filenames
.\jsn-to-txt.ps1 -IncludeTimestamp:$false
```

### **Parameters**

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `JsonFilePath` | String | `.\exchange\latest\tt\Sample-Exchange-Mailbox-Security-Results-Complete-50.json` | Path to the JSON audit file |
| `OutputDirectory` | String | `.\exchange\v1-6-7\output` | Directory for output files |
| `IncludeTimestamp` | Switch | `$true` | Include timestamp in output filenames |

## Output Files

### **Comprehensive Text Report**
- `Exchange-Audit-Report-[AuditID]-[Timestamp].txt`

**Report Contents:**
- **Executive Summary**: Compliance scores, risk assessments, and key metrics
- **Audit Metadata**: Complete environment and execution information
- **Security Controls Analysis**: Detailed findings for each control (MBX-1.1 through MBX-5.1)
- **Complete Permission Listings**: All permissions with full metadata including:
  - User details and department information
  - Assignment dates and last access times
  - Business justifications where available
  - Mailbox types and organizational context
  - Inheritance status and access rights details

## Report Structure Example

### **Text Report Structure**
```
================================================================================
EXCHANGE MAILBOX SECURITY AUDIT REPORT
================================================================================

EXECUTIVE SUMMARY
Overall Compliance Score: 82.4%
Assessment Success Rate: 100.0%

CONTROL ASSESSMENT SUMMARY:
  Total Controls Assessed: 5
  Controls Compliant: 3
  Controls Requiring Review: 1
  Controls Non-Compliant: 1

MAILBOX IMPERSONATION RIGHTS (MBX-1.1)
Control ID: MBX-1.1
Risk Level: Critical
CVSS Score: 8.5
Compliance Status: Review Required

IMPERSONATION RIGHTS COMPREHENSIVE DETAILS:
  Total Impersonation Rights: 8

  COMPLETE IMPERSONATION RIGHTS LISTING:
    [1] Impersonation Rights Entry:
        Mailbox: Exchange Service Account
        Email: <EMAIL>
        User Account: GLOBALENT\svc-backup
        Access Rights: ApplicationImpersonation
        Inherited: False
        Assignment Date: 2025-01-15
        Business Justification: Backup and archival operations

    [2] Impersonation Rights Entry:
        Mailbox: IT Admin Mailbox
        Email: <EMAIL>
        User Account: GLOBALENT\admin-exchange
        Access Rights: ApplicationImpersonation
        Inherited: False
        Assignment Date: 2025-02-20
        Business Justification: Exchange administration and troubleshooting
```

## Technical Details

### **JSON Structure Processing**
- **Comprehensive Data Extraction**: Processes all nested objects and arrays
- **Complete Permission Details**: Extracts all available metadata for each permission
- **Null Value Handling**: Safely handles missing or null data fields
- **Complex Array Processing**: Formats arrays of permission objects with full details
- **Professional Text Formatting**: Creates well-structured, readable output

### **Error Handling**
- File path validation
- JSON parsing error handling
- Directory creation error handling
- Text report generation error handling
- Graceful degradation on partial failures

### **Performance**
- Efficient memory usage for large JSON files
- Optimized text formatting for comprehensive reports
- Minimal PowerShell object overhead

## Use Cases

### **For Security Analysts**
- **Executive Reporting**: Comprehensive text report with high-level summaries and detailed findings
- **Risk Assessment**: Complete CVSS scores, compliance status, and risk levels for all controls
- **Compliance Documentation**: Professional formatting suitable for audit documentation and regulatory submissions

### **For Exchange Administrators**
- **Permission Reviews**: Complete permission listings with full metadata for thorough analysis
- **Remediation Planning**: Detailed findings and recommendations with specific user and mailbox information
- **Business Justification Review**: Access to business justifications and assignment dates for all permissions

### **For Compliance Officers**
- **Audit Trail**: Complete audit metadata, timestamps, and execution details
- **Comprehensive Reporting**: Single report containing all audit findings and detailed permission analysis
- **Regulatory Compliance**: Professional format with complete data suitable for regulatory submissions

## Requirements

- **PowerShell**: Version 5.1 or higher
- **Permissions**: Read access to JSON file, write access to output directory
- **Dependencies**: None (self-contained script)

## Compatibility

- **Exchange Versions**: Works with JSON output from Exchange 2016/2019/Online audits
- **Operating Systems**: Windows Server 2012 R2 and later, Windows 10/11
- **PowerShell Editions**: Desktop and Core editions supported

## Security Considerations

- **Read-Only Operations**: Script only reads JSON files and writes output files
- **No Exchange Access**: Does not connect to or modify Exchange servers
- **Data Handling**: Preserves all audit data without modification
- **File Permissions**: Respects existing file system permissions

## Troubleshooting

### **Common Issues**

1. **"JSON file not found"**
   - Verify the file path is correct
   - Ensure the file exists and is accessible

2. **"Failed to create output directory"**
   - Check write permissions to the parent directory
   - Verify disk space availability

3. **"Failed to load JSON data"**
   - Verify the JSON file is valid and not corrupted
   - Check if the file is in use by another process

### **Performance Optimization**
- For very large JSON files (>100MB), consider increasing PowerShell memory limits
- Use SSD storage for faster file I/O operations
- Close unnecessary applications to free up system resources

## Version History

- **v2.0.0** (September 12, 2025): Enhanced text-only version
  - Removed all CSV conversion functionality
  - Enhanced comprehensive text report with complete permission details
  - Added full metadata extraction for all permission types
  - Improved professional formatting for security analysts and auditors
  - Complete business justification and assignment date reporting

- **v1.0.0** (September 12, 2025): Initial release
  - CSV conversion with hierarchical structure preservation
  - Basic human-readable text report generation
  - Comprehensive error handling
  - Production-ready features

## Support

For issues or questions regarding this script:
1. Review the troubleshooting section above
2. Check PowerShell execution policies and permissions
3. Verify JSON file format compatibility with Exchange audit output
4. Contact E.Z. Consultancy for technical support

---
**Created By:** E.Z. Consultancy  
**Script Version:** 1.0.0  
**Compatible With:** Exchange-Mailbox-Security-Audit.ps1 v1.6.7  
**Last Updated:** September 12, 2025
